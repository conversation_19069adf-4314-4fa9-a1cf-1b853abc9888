using System;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class ARFloorOverlayFeature : ScriptableRendererFeature
{
    [System.Serializable]
    public class Settings
    {
        public Material material;
        public RenderPassEvent evt = RenderPassEvent.AfterRenderingOpaques;
    }

    public Settings settings = new Settings();
    private FloorOverlayPass _pass;

    public override void Create()
    {
        _pass = new FloorOverlayPass(settings)
        {
            renderPassEvent = settings.evt
        };
    }

    public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
    {
        if (settings.material == null)
            return;

        // Không enqueue pass nếu handle chưa sẵn sàng
        renderer.EnqueuePass(_pass);
    }


    private class FloorOverlayPass : ScriptableRenderPass
    {
        private readonly Settings _settings;
        private readonly string _profilerTag = "ARFloorOverlayPass";
        private RTHandle _cameraColorTargetHandle;

        public FloorOverlayPass(Settings settings)
        {
            _settings = settings;
            renderPassEvent = settings.evt;
        }

        public override void OnCameraSetup(CommandBuffer cmd, ref RenderingData renderingData)
        {
            _cameraColorTargetHandle = renderingData.cameraData.renderer.cameraColorTargetHandle;
            /*Debug.Log("OnCameraSetup handle valid? " + (_cameraColorTargetHandle != null));*/
        }

        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
            if (_settings.material == null || _cameraColorTargetHandle == null)
            {
                Debug.LogError("ARFloorOverlayPass: cameraColorTargetHandle is null!");
                return;
            }

            CommandBuffer cmd = CommandBufferPool.Get(_profilerTag);
            // Đặt RTHandle làm target
            CoreUtils.SetRenderTarget(cmd, _cameraColorTargetHandle);
            // Vẽ fullscreen quad với material overlay
            CoreUtils.DrawFullScreen(cmd, _settings.material);
            context.ExecuteCommandBuffer(cmd);
            CommandBufferPool.Release(cmd);
        }

        public override void FrameCleanup(CommandBuffer cmd)
        {
            // Không cần giải phóng RTHandle ở đây
        }
    }
}