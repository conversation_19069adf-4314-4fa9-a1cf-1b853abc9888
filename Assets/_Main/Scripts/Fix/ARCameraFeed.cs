using Unity.Collections;
using UnityEngine;
using UnityEngine.XR.ARFoundation;
using UnityEngine.XR.ARSubsystems;

[RequireComponent(typeof(ARCameraManager))]
public class ARCameraFeed : MonoBehaviour
{
    public ARCameraManager cameraManager;
    Texture2D cameraTex;

    void OnEnable() => cameraManager.frameReceived += OnFrame;
    void OnDisable() => cameraManager.frameReceived -= OnFrame;

    void OnFrame(ARCameraFrameEventArgs e)
    {
        if (!cameraManager.TryAcquireLatestCpuImage(out XRCpuImage img)) return;
        var conv = new XRCpuImage.ConversionParams
        {
            inputRect = new RectInt(0,0,img.width,img.height),
            outputDimensions = new Vector2Int(img.width, img.height),
            outputFormat = TextureFormat.RGB24,
            transformation = XRCpuImage.Transformation.MirrorX
        };
        var buf = new NativeArray<byte>(img.GetConvertedDataSize(conv), Allocator.Temp);
        img.Convert(conv, buf);
        img.Dispose();

        if (cameraTex == null || cameraTex.width != conv.outputDimensions.x)
            cameraTex = new Texture2D(conv.outputDimensions.x, conv.outputDimensions.y, conv.outputFormat, false);

        cameraTex.LoadRawTextureData(buf);
        cameraTex.Apply();
    }

    public Texture2D GetCameraTexture() => cameraTex;
}