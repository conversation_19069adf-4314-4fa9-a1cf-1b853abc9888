using UnityEngine;
using Unity.Barracuda;
using System.Collections;
using Unity.Collections;
using UnityEngine.XR.ARFoundation;
using UnityEngine.XR.ARSubsystems;

[RequireComponent(typeof(ARCameraManager))]
public class ARSegmentationRunner : MonoBehaviour
{
    [Header("Model & Barracuda")]
    public NNModel onnxModel;
    public Material overlayMat;    // Material chứa shader đọc _MaskTex

    [Header("AR Camera")]
    public ARCameraManager camMgr; // gắn ARCameraManager từ AR Camera

    const int SIZE = 520;          // Kích thước input/output model
    private Texture2D inputTex;     // Dùng để load CPU image ở native resolution
    private RenderTexture maskRT;   // RenderTexture chứa mask (R8)

    private Model runtimeModel;
    private IWorker worker;

    void Start()
    {
        // Load và khởi tạo Barracuda
        runtimeModel = ModelLoader.Load(onnxModel);
        worker = WorkerFactory.CreateWorker(WorkerFactory.Type.ComputePrecompiled, runtimeModel);

        maskRT = new RenderTexture(SIZE, SIZE, 0, RenderTextureFormat.R8);
        maskRT.Create();

        overlayMat.SetTexture("_MaskTex", maskRT);

        StartCoroutine(ProcessLoop());
    }

    IEnumerator ProcessLoop()
    {
        // ConversionParams: sẽ set kích thước per-frame so với ảnh gốc
        var convParams = new XRCpuImage.ConversionParams
        {
            inputRect       = new RectInt(),
            outputDimensions= new Vector2Int(),
            outputFormat    = TextureFormat.RGB24,
            transformation  = XRCpuImage.Transformation.MirrorX
        };

        while (true)
        {
            if (camMgr.TryAcquireLatestCpuImage(out XRCpuImage img))
            {
                // 1. Convert CPU image ở native size
                convParams.inputRect = new RectInt(0, 0, img.width, img.height);
                convParams.outputDimensions = new Vector2Int(img.width, img.height);

                int bufferSize = img.GetConvertedDataSize(convParams);
                var buffer = new NativeArray<byte>(bufferSize, Allocator.Temp);
                img.Convert(convParams, buffer);
                img.Dispose();

                // 2. Load vào Texture2D
                if (!inputTex ||
                    inputTex.width  != convParams.outputDimensions.x ||
                    inputTex.height != convParams.outputDimensions.y)
                {
                    inputTex = new Texture2D(
                        convParams.outputDimensions.x,
                        convParams.outputDimensions.y,
                        TextureFormat.RGB24,
                        false
                    );
                }
                inputTex.LoadRawTextureData(buffer);
                inputTex.Apply();
                buffer.Dispose();

                // 3. Resize xuống đúng SIZE x SIZE
                Texture2D resized = ResizeTexture(inputTex, SIZE, SIZE);

                // 4. Chạy Inference
                using (var t = new Tensor(resized, 3))
                {
                    worker.Execute(t);
                    Tensor outT = worker.PeekOutput(); // [1,1,520,520]

                    // 5. Tạo mask Texture2D (R8)
                    Texture2D tmpMask = new Texture2D(SIZE, SIZE, TextureFormat.R8, false);
                    Color32[] cols = new Color32[SIZE * SIZE];
                    for (int y = 0; y < SIZE; y++)
                        for (int x = 0; x < SIZE; x++)
                        {
                            float v = outT[0, 0, y, x];
                            byte b = (byte)(v > 0.5f ? 255 : 0);
                            cols[y * SIZE + x] = new Color32(b, b, b, 255);
                        }
                    tmpMask.SetPixels32(cols);
                    tmpMask.Apply();

                    // 6. Blit mask lên maskRT để shader đọc
                    Graphics.Blit(tmpMask, maskRT);

                    outT.Dispose();
                    Object.Destroy(tmpMask);
                }

                Object.Destroy(resized);
            }

            // Chạy ~10 FPS để giảm tải
            yield return new WaitForSeconds(0.1f);
        }
    }

    // Resize bằng RenderTexture
    Texture2D ResizeTexture(Texture2D src, int width, int height)
    {
        RenderTexture rt = RenderTexture.GetTemporary(width, height, 0, RenderTextureFormat.ARGB32);
        Graphics.Blit(src, rt);
        RenderTexture prev = RenderTexture.active;
        RenderTexture.active = rt;

        Texture2D dst = new Texture2D(width, height, src.format, false);
        dst.ReadPixels(new Rect(0, 0, width, height), 0, 0);
        dst.Apply();

        RenderTexture.active = prev;
        RenderTexture.ReleaseTemporary(rt);
        return dst;
    }

    void OnDisable()
    {
        worker.Dispose();
        maskRT.Release();
    }
}
