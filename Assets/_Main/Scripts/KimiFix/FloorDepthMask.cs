using UnityEngine;

public class FloorDepthMask : MonoBehaviour
{
    public Material floorMat;      // material dùng shader trên
    public Texture2D floorTexture;
    public float floorY = 0f;      // có thể set cứng hoặc ray-cast

    private Camera cam;

    void Awake()
    {
        cam = GetComponent<Camera>();
        if (floorMat) floorMat.SetTexture("_FloorTex", floorTexture);
    }

    void OnRenderImage(RenderTexture src, RenderTexture dst)
    {
        if (!floorMat)
        { Graphics.Blit(src, dst); return; }

        // Gửi inverse VP
        Matrix4x4 vp = GL.GetGPUProjectionMatrix(cam.projectionMatrix, false) * cam.worldToCameraMatrix;
        floorMat.SetMatrix("_InverseVP", vp.inverse);
        floorMat.SetFloat("_FloorY", floorY);

        Graphics.Blit(src, dst, floorMat);
    }
}