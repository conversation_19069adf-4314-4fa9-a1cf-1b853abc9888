using System.Collections;
using UnityEngine;
using CesiumForUnity;
using Unity.XR.CoreUtils;
using UnityEngine.XR.ARFoundation;

/// <summary>
/// Improved approach for aligning Cesium world with AR tracking
/// Fixes the issue where objects appear in front of camera instead of at correct geo locations
/// </summary>
public class CesiumARAligner : MonoBehaviour
{
    [Header("Core References")]
    public CesiumGeoreference cesiumReference;
    public Transform arCameraTransform;
    public XROrigin arSessionOrigin;
    
    [Header("Alignment Settings")]
    [SerializeField] private bool useECEFAlignment = true;
    [SerializeField] private bool compensateForHeading = true;
    [SerializeField] private float alignmentUpdateInterval = 1f;
    
    [Header("Debug")]
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private bool showDebugGizmos = false;
    
    // Private fields
    private GPSCompassManager gpsManager;
    private bool isAligned = false;
    private Vector3 initialARPosition;
    private Quaternion initialARRotation;
    private LocationInfo referenceLocation;
    private float referenceHeading;
    private Coroutine alignmentCoroutine;
    
    // ECEF (Earth-Centered, Earth-Fixed) coordinate system
    private Vector3 ecefOrigin;
    private Matrix4x4 enuToUnityMatrix;
    
    // Events
    public static event System.Action OnAlignmentComplete;
    public static event System.Action<string> OnAlignmentError;
    
    // Properties
    public bool IsAligned => isAligned;
    public Vector3 ECEFOrigin => ecefOrigin;
    
    private void Start()
    {
        InitializeAligner();
    }
    
    private void InitializeAligner()
    {
        // Get GPS manager
        gpsManager = GPSCompassManager.Instance;
        if (gpsManager == null)
        {
            OnAlignmentError?.Invoke("GPSCompassManager not found");
            return;
        }
        
        // Find AR components if not assigned
        if (arCameraTransform == null)
        {
            arCameraTransform = Camera.main?.transform;
        }
        
        /*if (arSessionOrigin == null)
        {
            arSessionOrigin = FindObjectOfType<ARSessionOrigin>();
        }*/
        
        if (cesiumReference == null)
        {
            cesiumReference = FindObjectOfType<CesiumGeoreference>();
        }
        
        // Wait for GPS initialization
        StartCoroutine(WaitForGPSAndAlign());
    }
    
    private IEnumerator WaitForGPSAndAlign()
    {
        // Wait for GPS to be ready
        while (!gpsManager.IsInitialized || !gpsManager.IsGPSRunning)
        {
            yield return new WaitForSeconds(0.5f);
        }
        
        // Wait a bit more for stable readings
        yield return new WaitForSeconds(2f);
        
        // Perform alignment
        PerformAlignment();
        
        // Start continuous alignment updates
        alignmentCoroutine = StartCoroutine(ContinuousAlignment());
    }
    
    private void PerformAlignment()
    {
        if (!gpsManager.IsGPSRunning)
        {
            OnAlignmentError?.Invoke("GPS not running");
            return;
        }
        
        // Store reference data
        referenceLocation = gpsManager.CurrentLocation;
        referenceHeading = gpsManager.CurrentHeading;
        initialARPosition = arCameraTransform.position;
        initialARRotation = arCameraTransform.rotation;
        
        if (useECEFAlignment)
        {
            PerformECEFAlignment();
        }
        else
        {
            PerformSimpleAlignment();
        }
        
        isAligned = true;
        OnAlignmentComplete?.Invoke();
        
        if (enableDebugLogs)
        {
            Debug.Log($"Cesium-AR alignment complete. Method: {(useECEFAlignment ? "ECEF" : "Simple")}");
            Debug.Log($"Reference location: {referenceLocation.latitude:F6}, {referenceLocation.longitude:F6}");
            Debug.Log($"Reference heading: {referenceHeading:F1}°");
        }
    }
    
    private void PerformECEFAlignment()
    {
        // Convert GPS coordinates to ECEF
        ecefOrigin = GeodeticToECEF(referenceLocation.latitude, referenceLocation.longitude, referenceLocation.altitude);
        
        // Set Cesium origin
        cesiumReference.SetOriginLongitudeLatitudeHeight(
            referenceLocation.longitude,
            referenceLocation.latitude,
            referenceLocation.altitude > 0 ? referenceLocation.altitude : 0
        );
        
        // Create ENU (East-North-Up) to Unity transformation matrix
        CreateENUToUnityMatrix();
        
        // Apply transformation to AR session origin
        ApplyENUTransformation();
    }
    
    private void PerformSimpleAlignment()
    {
        // Simple approach: just set Cesium origin without complex transformations
        cesiumReference.SetOriginLongitudeLatitudeHeight(
            referenceLocation.longitude,
            referenceLocation.latitude,
            referenceLocation.altitude > 0 ? referenceLocation.altitude : 0
        );
        
        // Keep Cesium world rotation at identity
        cesiumReference.transform.rotation = Quaternion.identity;
        
        // Apply heading compensation to AR session if needed
        if (compensateForHeading && arSessionOrigin != null)
        {
            // Create a parent object to handle the rotation offset
            GameObject headingCompensator = new GameObject("HeadingCompensator");
            headingCompensator.transform.position = Vector3.zero;
            headingCompensator.transform.rotation = Quaternion.Euler(0f, -referenceHeading, 0f);
            
            // Parent the AR session origin to the compensator
            arSessionOrigin.transform.SetParent(headingCompensator.transform, true);
        }
    }
    
    private Vector3 GeodeticToECEF(double latitude, double longitude, double altitude)
    {
        // WGS84 ellipsoid parameters
        const double a = 6378137.0; // Semi-major axis
        const double e2 = 0.00669437999014; // First eccentricity squared
        
        double lat = latitude * Mathf.Deg2Rad;
        double lon = longitude * Mathf.Deg2Rad;
        double h = altitude;
        
        double N = a / System.Math.Sqrt(1 - e2 * System.Math.Sin(lat) * System.Math.Sin(lat));
        
        double x = (N + h) * System.Math.Cos(lat) * System.Math.Cos(lon);
        double y = (N + h) * System.Math.Cos(lat) * System.Math.Sin(lon);
        double z = (N * (1 - e2) + h) * System.Math.Sin(lat);
        
        return new Vector3((float)x, (float)z, (float)y); // Convert to Unity coordinate system
    }
    
    private void CreateENUToUnityMatrix()
    {
        // Create transformation matrix from ENU (East-North-Up) to Unity coordinates
        double lat = referenceLocation.latitude * Mathf.Deg2Rad;
        double lon = referenceLocation.longitude * Mathf.Deg2Rad;
        
        // ENU basis vectors in ECEF
        Vector3 east = new Vector3(-(float)System.Math.Sin(lon), 0, (float)System.Math.Cos(lon));
        Vector3 north = new Vector3(
            -(float)(System.Math.Sin(lat) * System.Math.Cos(lon)),
            (float)System.Math.Cos(lat),
            -(float)(System.Math.Sin(lat) * System.Math.Sin(lon))
        );
        Vector3 up = new Vector3(
            (float)(System.Math.Cos(lat) * System.Math.Cos(lon)),
            (float)System.Math.Sin(lat),
            (float)(System.Math.Cos(lat) * System.Math.Sin(lon))
        );
        
        // Create transformation matrix (ENU to Unity: East->X, Up->Y, North->Z)
        enuToUnityMatrix = new Matrix4x4();
        enuToUnityMatrix.SetColumn(0, new Vector4(east.x, east.y, east.z, 0));
        enuToUnityMatrix.SetColumn(1, new Vector4(up.x, up.y, up.z, 0));
        enuToUnityMatrix.SetColumn(2, new Vector4(north.x, north.y, north.z, 0));
        enuToUnityMatrix.SetColumn(3, new Vector4(0, 0, 0, 1));
    }
    
    private void ApplyENUTransformation()
    {
        if (arSessionOrigin == null) return;
        
        // Create a transformation parent
        GameObject enuTransform = new GameObject("ENUTransform");
        enuTransform.transform.position = Vector3.zero;
        
        // Apply ENU to Unity transformation
        Matrix4x4 transformMatrix = enuToUnityMatrix;
        
        // Apply heading compensation
        if (compensateForHeading)
        {
            Matrix4x4 headingMatrix = Matrix4x4.Rotate(Quaternion.Euler(0f, -referenceHeading, 0f));
            transformMatrix = headingMatrix * transformMatrix;
        }
        
        // Extract rotation from matrix
        Quaternion rotation = transformMatrix.rotation;
        enuTransform.transform.rotation = rotation;
        
        // Parent AR session origin
        arSessionOrigin.transform.SetParent(enuTransform.transform, true);
    }
    
    private IEnumerator ContinuousAlignment()
    {
        while (isAligned)
        {
            yield return new WaitForSeconds(alignmentUpdateInterval);
            
            if (ShouldUpdateAlignment())
            {
                UpdateAlignment();
            }
        }
    }
    
    private bool ShouldUpdateAlignment()
    {
        if (!gpsManager.IsGPSRunning) return false;
        
        // Check for significant position drift
        var currentLocation = gpsManager.CurrentLocation;
        float positionDrift = CalculateDistance(
            referenceLocation.latitude, referenceLocation.longitude,
            currentLocation.latitude, currentLocation.longitude
        );
        
        // Check for significant heading drift
        float headingDrift = Mathf.Abs(Mathf.DeltaAngle(referenceHeading, gpsManager.CurrentHeading));
        
        return positionDrift > 10f || headingDrift > 15f; // Thresholds for re-alignment
    }
    
    private void UpdateAlignment()
    {
        if (enableDebugLogs)
        {
            Debug.Log("Updating Cesium-AR alignment due to drift");
        }
        
        // Store new reference data
        referenceLocation = gpsManager.CurrentLocation;
        referenceHeading = gpsManager.CurrentHeading;
        
        // Re-perform alignment
        if (useECEFAlignment)
        {
            PerformECEFAlignment();
        }
        else
        {
            PerformSimpleAlignment();
        }
    }
    
    private float CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371000; // Earth radius in meters
        double dLat = (lat2 - lat1) * Mathf.Deg2Rad;
        double dLon = (lon2 - lon1) * Mathf.Deg2Rad;
        
        double a = System.Math.Sin(dLat / 2) * System.Math.Sin(dLat / 2) +
                   System.Math.Cos(lat1 * Mathf.Deg2Rad) * System.Math.Cos(lat2 * Mathf.Deg2Rad) *
                   System.Math.Sin(dLon / 2) * System.Math.Sin(dLon / 2);
        
        double c = 2 * System.Math.Atan2(System.Math.Sqrt(a), System.Math.Sqrt(1 - a));
        return (float)(R * c);
    }
    
    // Public methods
    public void ForceRealignment()
    {
        if (gpsManager != null && gpsManager.IsGPSRunning)
        {
            PerformAlignment();
        }
    }
    
    public void SetAlignmentMethod(bool useECEF)
    {
        useECEFAlignment = useECEF;
        if (isAligned)
        {
            ForceRealignment();
        }
    }
    
    public Vector3 ConvertGeodeticToUnity(double latitude, double longitude, double altitude)
    {
        if (!isAligned) return Vector3.zero;
        
        // Convert to local ENU coordinates relative to reference point
        double deltaLat = (latitude - referenceLocation.latitude) * Mathf.Deg2Rad;
        double deltaLon = (longitude - referenceLocation.longitude) * Mathf.Deg2Rad;
        double deltaAlt = altitude - referenceLocation.altitude;
        
        // Approximate conversion for small distances
        double earthRadius = 6371000;
        float east = (float)(deltaLon * earthRadius * System.Math.Cos(referenceLocation.latitude * Mathf.Deg2Rad));
        float north = (float)(deltaLat * earthRadius);
        float up = (float)deltaAlt;
        
        // Transform to Unity coordinates
        Vector3 enuPosition = new Vector3(east, up, north);
        
        if (useECEFAlignment)
        {
            return enuToUnityMatrix.MultiplyPoint3x4(enuPosition);
        }
        else
        {
            // Simple transformation: East->X, Up->Y, North->Z
            return new Vector3(east, up, north);
        }
    }
    
    private void OnDrawGizmos()
    {
        if (!showDebugGizmos || !isAligned) return;
        
        // Draw reference point
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(Vector3.zero, 1f);
        
        // Draw coordinate axes
        Gizmos.color = Color.red;
        Gizmos.DrawLine(Vector3.zero, Vector3.right * 10f); // East
        
        Gizmos.color = Color.green;
        Gizmos.DrawLine(Vector3.zero, Vector3.up * 10f); // Up
        
        Gizmos.color = Color.blue;
        Gizmos.DrawLine(Vector3.zero, Vector3.forward * 10f); // North
    }
    
    private void OnDestroy()
    {
        if (alignmentCoroutine != null)
        {
            StopCoroutine(alignmentCoroutine);
        }
        
        OnAlignmentComplete = null;
        OnAlignmentError = null;
    }
}
