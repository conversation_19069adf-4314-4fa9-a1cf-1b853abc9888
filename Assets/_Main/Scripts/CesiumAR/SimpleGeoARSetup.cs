using System.Collections;
using UnityEngine;
using CesiumForUnity;
using UnityEngine.XR.ARFoundation;
using System;

[RequireComponent(typeof(ARSession))]
public class SimpleGeoARSetup : MonoBehaviour
{
    [Header("Cesium Reference")]
    public CesiumGeoreference cesiumReference;

    [Header("GPS Settings")]
    [SerializeField] private float desiredAccuracyInMeters = 10f;
    [SerializeField] private float updateDistanceInMeters = 5f;
    [SerializeField] private int maxWaitTimeSeconds = 30;

    [Header("Compass Settings")]
    [SerializeField] private bool enableCompassCalibration = true;
    [SerializeField] private float compassSmoothingFactor = 0.1f;

    [Header("Debug")]
    [SerializeField] private bool enableDebugLogs = true;

    // Events
    public static event Action<LocationInfo> OnLocationUpdated;
    public static event Action<float> OnHeadingUpdated;
    public static event Action<string> OnLocationError;

    // Private fields
    private bool isInitialized = false;
    private float lastHeading = 0f;
    private LocationInfo lastLocation;
    private Coroutine trackingCoroutine;

    // Properties
    public bool IsGPSRunning => Input.location.status == LocationServiceStatus.Running;
    public bool IsCompassEnabled => Input.compass.enabled;
    public LocationInfo CurrentLocation => Input.location.lastData;
    public float CurrentHeading => Input.compass.trueHeading;

    private void Start()
    {
        StartCoroutine(InitializeGeoAR());
    }

    private IEnumerator RequestLocationPermission()
    {
#if UNITY_ANDROID
        if (!UnityEngine.Android.Permission.HasUserAuthorizedPermission(UnityEngine.Android.Permission.FineLocation))
        {
            UnityEngine.Android.Permission.RequestUserPermission(UnityEngine.Android.Permission.FineLocation);

            // Wait for user response
            float timeout = 10f;
            while (!UnityEngine.Android.Permission.HasUserAuthorizedPermission(UnityEngine.Android.Permission.FineLocation) && timeout > 0)
            {
                yield return new WaitForSeconds(0.1f);
                timeout -= 0.1f;
            }

            if (!UnityEngine.Android.Permission.HasUserAuthorizedPermission(UnityEngine.Android.Permission.FineLocation))
            {
                OnLocationError?.Invoke("Location permission denied by user");
                yield break;
            }
        }
#endif
        yield return null;
    }

    private IEnumerator InitializeGeoAR()
    {
        if (enableDebugLogs) Debug.Log("Starting Geo-AR initialization...");

        // 1. Request location permission
        yield return StartCoroutine(RequestLocationPermission());

        // 2. Start location services
        if (!Input.location.isEnabledByUser)
        {
            OnLocationError?.Invoke("Location services disabled by user");
            yield break;
        }

        Input.location.Start(desiredAccuracyInMeters, updateDistanceInMeters);
        Input.compass.enabled = true;

        // 3. Wait for GPS to initialize
        int maxWait = maxWaitTimeSeconds;
        while (Input.location.status == LocationServiceStatus.Initializing && maxWait > 0)
        {
            if (enableDebugLogs) Debug.Log($"Waiting for GPS... {maxWait}s remaining");
            yield return new WaitForSeconds(1);
            maxWait--;
        }

        // 4. Check GPS status
        if (Input.location.status != LocationServiceStatus.Running)
        {
            string error = $"GPS failed to start. Status: {Input.location.status}";
            OnLocationError?.Invoke(error);
            if (enableDebugLogs) Debug.LogError(error);
            yield break;
        }

        // 5. Wait for compass to stabilize
        yield return new WaitForSeconds(2f);

        // 6. Initialize Cesium origin
        yield return StartCoroutine(SetupCesiumOrigin());

        // 7. Start continuous tracking
        isInitialized = true;
        trackingCoroutine = StartCoroutine(ContinuousTracking());

        if (enableDebugLogs) Debug.Log("Geo-AR initialization completed successfully!");
    }

    private IEnumerator SetupCesiumOrigin()
    {
        if (cesiumReference == null)
        {
            OnLocationError?.Invoke("CesiumGeoreference not assigned!");
            yield break;
        }

        // Get initial location and heading
        var location = Input.location.lastData;
        float heading = GetSmoothedHeading();

        // Validate location data
        if (location.latitude == 0 && location.longitude == 0)
        {
            OnLocationError?.Invoke("Invalid GPS coordinates received");
            yield break;
        }

        // Set Cesium origin to device location
        cesiumReference.SetOriginLongitudeLatitudeHeight(
            location.longitude,
            location.latitude,
            location.altitude > 0 ? location.altitude : 0
        );

        // Align Cesium world with true north
        cesiumReference.transform.rotation = Quaternion.Euler(0f, -heading, 0f);

        // Store initial values
        lastLocation = location;
        lastHeading = heading;

        // Fire events
        OnLocationUpdated?.Invoke(location);
        OnHeadingUpdated?.Invoke(heading);

        if (enableDebugLogs)
        {
            Debug.Log($"Cesium origin set: Lon={location.longitude:F6}, Lat={location.latitude:F6}, " +
                     $"Alt={location.altitude:F1}, Heading={heading:F1}°");
        }
    }

    private IEnumerator ContinuousTracking()
    {
        while (isInitialized && IsGPSRunning)
        {
            // Update location if changed significantly
            var currentLocation = Input.location.lastData;
            if (HasLocationChanged(currentLocation, lastLocation))
            {
                lastLocation = currentLocation;
                OnLocationUpdated?.Invoke(currentLocation);

                if (enableDebugLogs)
                {
                    Debug.Log($"Location updated: Lon={currentLocation.longitude:F6}, Lat={currentLocation.latitude:F6}");
                }
            }

            // Update heading if changed significantly
            float currentHeading = GetSmoothedHeading();
            if (Mathf.Abs(Mathf.DeltaAngle(currentHeading, lastHeading)) > 5f)
            {
                lastHeading = currentHeading;
                OnHeadingUpdated?.Invoke(currentHeading);

                // Update Cesium rotation
                if (cesiumReference != null)
                {
                    cesiumReference.transform.rotation = Quaternion.Euler(0f, -currentHeading, 0f);
                }

                if (enableDebugLogs)
                {
                    Debug.Log($"Heading updated: {currentHeading:F1}°");
                }
            }

            yield return new WaitForSeconds(0.5f); // Update every 0.5 seconds
        }
    }

    private float GetSmoothedHeading()
    {
        float rawHeading = Input.compass.trueHeading;

        // Handle compass calibration
        if (enableCompassCalibration && Input.compass.headingAccuracy > 20f)
        {
            if (enableDebugLogs) Debug.LogWarning("Compass needs calibration. Accuracy: " + Input.compass.headingAccuracy);
        }

        // Smooth heading to reduce jitter
        if (lastHeading != 0f)
        {
            float angleDiff = Mathf.DeltaAngle(lastHeading, rawHeading);
            rawHeading = lastHeading + angleDiff * compassSmoothingFactor;
        }

        return rawHeading;
    }

    private bool HasLocationChanged(LocationInfo current, LocationInfo last)
    {
        if (last.latitude == 0 && last.longitude == 0) return true;

        float distance = CalculateDistance(
            current.latitude, current.longitude,
            last.latitude, last.longitude
        );

        return distance > updateDistanceInMeters;
    }

    private float CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371000; // Earth radius in meters
        double dLat = (lat2 - lat1) * Mathf.Deg2Rad;
        double dLon = (lon2 - lon1) * Mathf.Deg2Rad;

        double a = Mathf.Sin((float)dLat / 2) * Mathf.Sin((float)dLat / 2) +
                   Mathf.Cos((float)(lat1 * Mathf.Deg2Rad)) * Mathf.Cos((float)(lat2 * Mathf.Deg2Rad)) *
                   Mathf.Sin((float)dLon / 2) * Mathf.Sin((float)dLon / 2);

        double c = 2 * Mathf.Atan2(Mathf.Sqrt((float)a), Mathf.Sqrt((float)(1 - a)));
        return (float)(R * c);
    }

    public void RecalibrateOrigin()
    {
        if (IsGPSRunning && cesiumReference != null)
        {
            StartCoroutine(SetupCesiumOrigin());
        }
    }

    public void StopTracking()
    {
        isInitialized = false;
        if (trackingCoroutine != null)
        {
            StopCoroutine(trackingCoroutine);
            trackingCoroutine = null;
        }
    }

    private void OnDisable()
    {
        StopTracking();
        Input.location.Stop();
        Input.compass.enabled = false;
    }

    private void OnDestroy()
    {
        OnLocationUpdated = null;
        OnHeadingUpdated = null;
        OnLocationError = null;
    }
}
