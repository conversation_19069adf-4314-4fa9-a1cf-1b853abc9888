using System.Collections;
using UnityEngine;
using CesiumForUnity;
using UnityEngine.XR.ARFoundation;

[RequireComponent(typeof(ARSession))]
public class SimpleGeoARSetup : MonoBehaviour
{
    [Header("Cesium Reference (gắn trong Inspector)")]
    public CesiumGeoreference cesiumReference;
    
    IEnumerator RequestLocationPermission()
    {
#if UNITY_ANDROID
        if (!UnityEngine.Android.Permission.HasUserAuthorizedPermission(UnityEngine.Android.Permission.FineLocation))
        {
            UnityEngine.Android.Permission.RequestUserPermission(UnityEngine.Android.Permission.FineLocation);
            yield return new WaitForSeconds(1.0f); // chờ người dùng chọn
        }
#endif
    }

#if UNITY_ANDROID
    IEnumerator Start()
    {
        // 1. Bật GPS + Compass
        yield return RequestLocationPermission();
        Input.location.Start();
        Input.compass.enabled = true;

        // 2. Đợi GPS khởi động
        int maxWait = 20;
        while (Input.location.status == LocationServiceStatus.Initializing && maxWait > 0)
        {
            yield return new WaitForSeconds(1);
            maxWait--;
        }

        if (Input.location.status != LocationServiceStatus.Running)
        {
            Debug.LogError("GPS không hoạt động!");
            yield break;
        }

        // 3. Lấy thông số thiết bị
        var loc = Input.location.lastData;
        double deviceLon = loc.longitude;
        double deviceLat = loc.latitude;
        double deviceAlt = loc.altitude;
        float heading   = Input.compass.trueHeading;

        // 4. Set origin & xoay Cesium world
        cesiumReference.SetOriginLongitudeLatitudeHeight(
            deviceLon, deviceLat, deviceAlt);
        cesiumReference.transform.rotation =
            Quaternion.Euler(0f, -heading, 0f);

        Debug.Log(
            $"Init AR-Geo: Lon={deviceLon:F6}, Lat={deviceLat:F6}, " +
            $"Alt={deviceAlt:F1}, Heading={heading:F1}"
        );
    }
#endif

    void OnDisable()
    {
        Input.location.Stop();
        Input.compass.enabled = false;
    }
}
