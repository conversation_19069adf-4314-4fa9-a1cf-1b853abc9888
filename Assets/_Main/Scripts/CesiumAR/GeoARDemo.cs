using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Demo script showing how to use the Geo-AR system
/// Demonstrates spawning objects at specific geographic locations
/// </summary>
public class GeoARDemo : MonoBehaviour
{
    [Header("Demo Objects")]
    [SerializeField] private GameObject[] demoPrefabs;
    [SerializeField] private bool spawnDemoObjectsOnStart = true;
    
    [Header("Demo Locations (Vietnam Examples)")]
    [SerializeField] private DemoLocation[] demoLocations = new DemoLocation[]
    {
        new DemoLocation
        {
            name = "Hoan Kiem Lake",
            longitude = 105.8522,
            latitude = 21.0285,
            altitude = 10,
            description = "Historic lake in Hanoi"
        },
        new DemoLocation
        {
            name = "Ben Thanh Market",
            longitude = 106.6981,
            latitude = 10.7720,
            altitude = 5,
            description = "Famous market in Ho Chi Minh City"
        },
        new DemoLocation
        {
            name = "Hoi An Ancient Town",
            longitude = 108.3380,
            latitude = 15.8801,
            altitude = 3,
            description = "UNESCO World Heritage Site"
        }
    };
    
    [Header("UI References")]
    [SerializeField] private Button spawnObjectButton;
    [SerializeField] private Button clearObjectsButton;
    [SerializeField] private Button recalibrateButton;
    [SerializeField] private Dropdown locationDropdown;
    [SerializeField] private Text infoText;
    [SerializeField] private Text gpsInfoText;
    [SerializeField] private Slider distanceSlider;
    
    [Header("Runtime Settings")]
    [SerializeField] private float maxSpawnDistance = 1000f; // meters
    [SerializeField] private bool showDistanceToObjects = true;
    [SerializeField] private bool autoUpdateInfo = true;
    
    // Private fields
    private SpawnAnchoredObject objectSpawner;
    private GPSCompassManager gpsManager;
    private GeoARManager geoARManager;
    private List<string> spawnedObjectIds = new List<string>();
    private int nextObjectId = 0;
    
    [System.Serializable]
    public class DemoLocation
    {
        public string name;
        public double longitude;
        public double latitude;
        public double altitude;
        public string description;
    }
    
    private void Start()
    {
        InitializeDemo();
    }
    
    private void InitializeDemo()
    {
        // Get references to managers
        geoARManager = GeoARManager.Instance;
        gpsManager = GPSCompassManager.Instance;
        objectSpawner = FindObjectOfType<SpawnAnchoredObject>();
        
        // Setup UI
        SetupUI();
        
        // Subscribe to events
        SubscribeToEvents();
        
        // Wait for system initialization
        if (geoARManager != null && geoARManager.IsInitialized)
        {
            OnSystemInitialized();
        }
    }
    
    private void SetupUI()
    {
        // Setup buttons
        if (spawnObjectButton != null)
        {
            spawnObjectButton.onClick.AddListener(SpawnObjectAtSelectedLocation);
        }
        
        if (clearObjectsButton != null)
        {
            clearObjectsButton.onClick.AddListener(ClearAllObjects);
        }
        
        if (recalibrateButton != null)
        {
            recalibrateButton.onClick.AddListener(RecalibrateSystem);
        }
        
        // Setup location dropdown
        if (locationDropdown != null)
        {
            locationDropdown.ClearOptions();
            List<string> locationNames = new List<string>();
            
            foreach (var location in demoLocations)
            {
                locationNames.Add($"{location.name} ({location.description})");
            }
            
            locationDropdown.AddOptions(locationNames);
        }
        
        // Setup distance slider
        if (distanceSlider != null)
        {
            distanceSlider.minValue = 10f;
            distanceSlider.maxValue = maxSpawnDistance;
            distanceSlider.value = 100f;
            distanceSlider.onValueChanged.AddListener(OnDistanceSliderChanged);
        }
        
        UpdateInfoText("Initializing Geo-AR system...");
    }
    
    private void SubscribeToEvents()
    {
        if (geoARManager != null)
        {
            GeoARManager.OnSystemInitialized += OnSystemInitialized;
            GeoARManager.OnSystemError += OnSystemError;
            GeoARManager.OnCalibrationComplete += OnCalibrationComplete;
        }
        
        if (gpsManager != null)
        {
            GPSCompassManager.OnLocationUpdated += OnLocationUpdated;
            GPSCompassManager.OnHeadingUpdated += OnHeadingUpdated;
        }
        
        if (objectSpawner != null)
        {
            SpawnAnchoredObject.OnObjectSpawned += OnObjectSpawned;
            SpawnAnchoredObject.OnObjectDestroyed += OnObjectDestroyed;
        }
    }
    
    private void OnSystemInitialized()
    {
        UpdateInfoText("Geo-AR system ready!");
        
        if (spawnDemoObjectsOnStart)
        {
            SpawnDemoObjects();
        }
        
        // Enable UI
        if (spawnObjectButton != null) spawnObjectButton.interactable = true;
        if (clearObjectsButton != null) clearObjectsButton.interactable = true;
        if (recalibrateButton != null) recalibrateButton.interactable = true;
    }
    
    private void OnSystemError(string error)
    {
        UpdateInfoText($"System Error: {error}");
        
        // Disable UI
        if (spawnObjectButton != null) spawnObjectButton.interactable = false;
        if (clearObjectsButton != null) clearObjectsButton.interactable = false;
    }
    
    private void OnCalibrationComplete()
    {
        UpdateInfoText("System calibrated successfully!");
    }
    
    private void OnLocationUpdated(LocationInfo location)
    {
        if (autoUpdateInfo)
        {
            UpdateGPSInfo();
        }
    }
    
    private void OnHeadingUpdated(float heading)
    {
        if (autoUpdateInfo)
        {
            UpdateGPSInfo();
        }
    }
    
    private void OnObjectSpawned(GeoAnchoredObject geoObject)
    {
        Debug.Log($"Object spawned: {geoObject.objectId}");
        UpdateInfoText($"Object spawned: {geoObject.objectId}");
    }
    
    private void OnObjectDestroyed(GeoAnchoredObject geoObject)
    {
        Debug.Log($"Object destroyed: {geoObject.objectId}");
        spawnedObjectIds.Remove(geoObject.objectId);
    }
    
    private void SpawnDemoObjects()
    {
        if (objectSpawner == null || demoPrefabs.Length == 0) return;
        
        // Spawn one object at each demo location
        for (int i = 0; i < demoLocations.Length && i < demoPrefabs.Length; i++)
        {
            SpawnObjectAtLocation(demoLocations[i], demoPrefabs[i]);
        }
    }
    
    private void SpawnObjectAtSelectedLocation()
    {
        if (locationDropdown == null || demoPrefabs.Length == 0) return;
        
        int selectedIndex = locationDropdown.value;
        if (selectedIndex >= 0 && selectedIndex < demoLocations.Length)
        {
            var location = demoLocations[selectedIndex];
            var prefab = demoPrefabs[Random.Range(0, demoPrefabs.Length)];
            
            SpawnObjectAtLocation(location, prefab);
        }
    }
    
    private void SpawnObjectAtLocation(DemoLocation location, GameObject prefab)
    {
        if (objectSpawner == null) return;
        
        // Check distance from current location
        if (gpsManager != null && gpsManager.IsGPSRunning)
        {
            float distance = gpsManager.GetDistanceTo(location.latitude, location.longitude);
            
            if (distance > maxSpawnDistance)
            {
                UpdateInfoText($"Location too far: {distance:F0}m (max: {maxSpawnDistance:F0}m)");
                return;
            }
        }
        
        // Create geo object
        var geoObject = new GeoAnchoredObject
        {
            objectId = $"demo_object_{nextObjectId++}",
            prefab = prefab,
            longitude = location.longitude,
            latitude = location.latitude,
            altitude = location.altitude,
            maxVisibilityDistance = distanceSlider != null ? distanceSlider.value : 500f,
            minVisibilityDistance = 0f
        };
        
        // Add to spawner
        objectSpawner.AddGeoObject(geoObject);
        spawnedObjectIds.Add(geoObject.objectId);
        
        UpdateInfoText($"Spawned object at {location.name}");
    }
    
    private void ClearAllObjects()
    {
        if (objectSpawner == null) return;
        
        foreach (var objectId in spawnedObjectIds)
        {
            objectSpawner.RemoveGeoObject(objectId);
        }
        
        spawnedObjectIds.Clear();
        UpdateInfoText("All objects cleared");
    }
    
    private void RecalibrateSystem()
    {
        if (geoARManager != null)
        {
            geoARManager.ManualRecalibrate();
            UpdateInfoText("Recalibrating system...");
        }
    }
    
    private void OnDistanceSliderChanged(float value)
    {
        UpdateInfoText($"Max visibility distance: {value:F0}m");
    }
    
    private void UpdateInfoText(string message)
    {
        if (infoText != null)
        {
            infoText.text = $"[{System.DateTime.Now:HH:mm:ss}] {message}";
        }
        
        Debug.Log($"GeoARDemo: {message}");
    }
    
    private void UpdateGPSInfo()
    {
        if (gpsInfoText == null || gpsManager == null) return;
        
        string info = "";
        
        if (gpsManager.IsGPSRunning)
        {
            var location = gpsManager.CurrentLocation;
            info += $"GPS: {location.latitude:F6}, {location.longitude:F6}\n";
            info += $"Altitude: {location.altitude:F1}m\n";
            info += $"Accuracy: {gpsManager.LocationAccuracy:F1}m\n";
        }
        else
        {
            info += "GPS: Not available\n";
        }
        
        if (gpsManager.IsCompassEnabled)
        {
            info += $"Heading: {gpsManager.CurrentHeading:F1}°\n";
        }
        else
        {
            info += "Compass: Not available\n";
        }
        
        info += $"Objects: {spawnedObjectIds.Count}";
        
        gpsInfoText.text = info;
    }
    
    private void Update()
    {
        // Update GPS info periodically
        if (autoUpdateInfo && Time.frameCount % 30 == 0) // Every 0.5 seconds at 60fps
        {
            UpdateGPSInfo();
        }
        
        // Update object distances if enabled
        if (showDistanceToObjects && gpsManager != null && gpsManager.IsGPSRunning)
        {
            UpdateObjectDistances();
        }
    }
    
    private void UpdateObjectDistances()
    {
        if (objectSpawner == null) return;
        
        foreach (var objectId in spawnedObjectIds)
        {
            var geoObject = objectSpawner.GetGeoObject(objectId);
            if (geoObject != null && geoObject.SpawnedObject != null)
            {
                float distance = gpsManager.GetDistanceTo(geoObject.latitude, geoObject.longitude);
                
                // You could add a UI element to show distance here
                // For example, a world space canvas with distance text
            }
        }
    }
    
    // Public methods for UI buttons
    public void OnSpawnRandomObject()
    {
        if (demoLocations.Length > 0 && demoPrefabs.Length > 0)
        {
            var randomLocation = demoLocations[Random.Range(0, demoLocations.Length)];
            var randomPrefab = demoPrefabs[Random.Range(0, demoPrefabs.Length)];
            
            SpawnObjectAtLocation(randomLocation, randomPrefab);
        }
    }
    
    public void OnToggleObjectVisibility()
    {
        if (objectSpawner != null && spawnedObjectIds.Count > 0)
        {
            var randomObjectId = spawnedObjectIds[Random.Range(0, spawnedObjectIds.Count)];
            var geoObject = objectSpawner.GetGeoObject(randomObjectId);
            
            if (geoObject != null)
            {
                objectSpawner.SetObjectVisibility(randomObjectId, !geoObject.IsVisible);
                UpdateInfoText($"Toggled visibility for {randomObjectId}");
            }
        }
    }
    
    public void OnRecalculateDistances()
    {
        if (objectSpawner != null)
        {
            objectSpawner.RecalculateAllDistances();
            UpdateInfoText("Recalculated all object distances");
        }
    }
    
    private void OnDestroy()
    {
        // Unsubscribe from events
        if (geoARManager != null)
        {
            GeoARManager.OnSystemInitialized -= OnSystemInitialized;
            GeoARManager.OnSystemError -= OnSystemError;
            GeoARManager.OnCalibrationComplete -= OnCalibrationComplete;
        }
        
        if (gpsManager != null)
        {
            GPSCompassManager.OnLocationUpdated -= OnLocationUpdated;
            GPSCompassManager.OnHeadingUpdated -= OnHeadingUpdated;
        }
        
        if (objectSpawner != null)
        {
            SpawnAnchoredObject.OnObjectSpawned -= OnObjectSpawned;
            SpawnAnchoredObject.OnObjectDestroyed -= OnObjectDestroyed;
        }
    }
}
