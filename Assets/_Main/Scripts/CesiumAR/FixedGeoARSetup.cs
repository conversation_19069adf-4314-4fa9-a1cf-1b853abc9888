using System.Collections;
using UnityEngine;
using CesiumForUnity;
using UnityEngine.XR.ARFoundation;
using Unity.XR.CoreUtils;

/// <summary>
/// Fixed approach for Geo-AR setup that properly handles coordinate systems
/// Ensures objects spawn at correct geographic locations, not in front of camera
/// </summary>
public class FixedGeoARSetup : MonoBehaviour
{
    [Header("Core References")]
    public CesiumGeoreference cesiumReference;
    public XROrigin xrOrigin;
    public Camera arCamera;
    
    [Header("Settings")]
    [SerializeField] private bool autoInitialize = true;
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private bool useAbsolutePositioning = true;
    
    [Header("Debug")]
    [SerializeField] private bool showDebugInfo = false;
    [SerializeField] private UnityEngine.UI.Text debugText;
    
    // Private fields
    private GPSCompassManager gpsManager;
    private bool isInitialized = false;
    private LocationInfo originLocation;
    private float originHeading;
    
    // Events
    public static event System.Action OnSetupComplete;
    public static event System.Action<string> OnSetupError;
    
    // Properties
    public bool IsInitialized => isInitialized;
    public LocationInfo OriginLocation => originLocation;
    
    private void Start()
    {
        if (autoInitialize)
        {
            StartCoroutine(InitializeGeoAR());
        }
    }
    
    private IEnumerator InitializeGeoAR()
    {
        if (enableDebugLogs) Debug.Log("Starting Fixed Geo-AR initialization...");
        
        // Find components if not assigned
        FindRequiredComponents();
        
        // Wait for GPS manager
        gpsManager = GPSCompassManager.Instance;
        while (gpsManager == null || !gpsManager.IsInitialized)
        {
            yield return new WaitForSeconds(0.5f);
            gpsManager = GPSCompassManager.Instance;
        }
        
        // Wait for stable GPS reading
        yield return new WaitForSeconds(3f);
        
        // Perform setup
        SetupCesiumOrigin();
        
        // CRITICAL: Do NOT rotate anything - keep everything at identity
        EnsureNoRotations();
        
        isInitialized = true;
        OnSetupComplete?.Invoke();
        
        if (enableDebugLogs)
        {
            Debug.Log($"Fixed Geo-AR setup complete!");
            Debug.Log($"Origin: {originLocation.latitude:F8}, {originLocation.longitude:F8}");
            Debug.Log($"Cesium rotation: {cesiumReference.transform.rotation.eulerAngles}");
            Debug.Log($"XR Origin rotation: {xrOrigin.transform.rotation.eulerAngles}");
        }
    }
    
    private void FindRequiredComponents()
    {
        if (cesiumReference == null)
        {
            cesiumReference = FindObjectOfType<CesiumGeoreference>();
        }
        
        if (xrOrigin == null)
        {
            xrOrigin = FindObjectOfType<XROrigin>();
        }
        
        if (arCamera == null)
        {
            arCamera = Camera.main;
            if (arCamera == null)
            {
                arCamera = FindObjectOfType<Camera>();
            }
        }
        
        // Validate components
        if (cesiumReference == null)
        {
            OnSetupError?.Invoke("CesiumGeoreference not found!");
            return;
        }
        
        if (xrOrigin == null)
        {
            OnSetupError?.Invoke("XROrigin not found!");
            return;
        }
    }
    
    private void SetupCesiumOrigin()
    {
        if (!gpsManager.IsGPSRunning)
        {
            OnSetupError?.Invoke("GPS not running");
            return;
        }
        
        // Get current GPS location
        originLocation = gpsManager.CurrentLocation;
        originHeading = gpsManager.CurrentHeading;
        
        // Set Cesium origin to current GPS location
        cesiumReference.SetOriginLongitudeLatitudeHeight(
            originLocation.longitude,
            originLocation.latitude,
            originLocation.altitude > 0 ? originLocation.altitude : 0
        );
        
        if (enableDebugLogs)
        {
            Debug.Log($"Cesium origin set to: {originLocation.latitude:F8}, {originLocation.longitude:F8}, {originLocation.altitude:F2}");
        }
    }
    
    private void EnsureNoRotations()
    {
        // CRITICAL: Keep all rotations at identity to prevent objects appearing in front of camera
        
        // Ensure Cesium world has no rotation
        cesiumReference.transform.rotation = Quaternion.identity;
        
        // Ensure XR Origin has no additional rotation
        xrOrigin.transform.rotation = Quaternion.identity;
        
        // Remove any existing rotation compensators
        var allTransforms = FindObjectsOfType<Transform>();
        var existingCompensators = new System.Collections.Generic.List<Transform>();

        foreach (var t in allTransforms)
        {
            if (t.name.Contains("NorthAlignment") ||
                t.name.Contains("HeadingCompensator") ||
                t.name.Contains("ENUTransform"))
            {
                existingCompensators.Add(t);
            }
        }

        foreach (var compensator in existingCompensators)
        {
            if (compensator != null && compensator.gameObject != null)
            {
                // Unparent children first
                var children = new Transform[compensator.childCount];
                for (int i = 0; i < compensator.childCount; i++)
                {
                    children[i] = compensator.GetChild(i);
                }
                
                foreach (var child in children)
                {
                    child.SetParent(null, true);
                }
                
                DestroyImmediate(compensator.gameObject);
            }
        }
        
        if (enableDebugLogs)
        {
            Debug.Log("All rotations reset to identity - objects will spawn at correct geo locations");
        }
    }
    
    // Public method to spawn object at specific geo location
    public GameObject SpawnObjectAtLocation(GameObject prefab, double longitude, double latitude, double altitude)
    {
        if (!isInitialized)
        {
            Debug.LogError("FixedGeoARSetup not initialized yet!");
            return null;
        }
        
        if (prefab == null)
        {
            Debug.LogError("Prefab is null!");
            return null;
        }
        
        // Spawn object as child of Cesium world
        GameObject obj = Instantiate(prefab, cesiumReference.transform);
        
        // Add CesiumGlobeAnchor
        var anchor = obj.GetComponent<CesiumGlobeAnchor>();
        if (anchor == null)
        {
            anchor = obj.AddComponent<CesiumGlobeAnchor>();
        }
        
        // Set geographic position
        anchor.SetPositionLongitudeLatitudeHeight(longitude, latitude, altitude);
        anchor.adjustOrientationForGlobeWhenMoving = false; // Keep object orientation fixed
        anchor.detectTransformChanges = false;
        
        if (enableDebugLogs)
        {
            Debug.Log($"Spawned object at {latitude:F8}, {longitude:F8}, {altitude:F2}");
            Debug.Log($"Unity position: {obj.transform.position}");
            Debug.Log($"Distance from origin: {Vector3.Distance(Vector3.zero, obj.transform.position):F2}m");
        }
        
        return obj;
    }
    
    // Test method to spawn objects at known relative positions
    [ContextMenu("Test Spawn Objects")]
    public void TestSpawnObjects()
    {
        if (!isInitialized)
        {
            Debug.LogWarning("Not initialized yet!");
            return;
        }
        
        // Create a simple test cube if no prefab provided
        GameObject testCube = GameObject.CreatePrimitive(PrimitiveType.Cube);
        testCube.transform.localScale = Vector3.one * 2f;
        testCube.GetComponent<Renderer>().material.color = Color.red;
        
        // Test locations relative to current position
        var testLocations = new[]
        {
            new { name = "North 50m", latOffset = 50.0 / 111320.0, lonOffset = 0.0 },
            new { name = "East 50m", latOffset = 0.0, lonOffset = 50.0 / (111320.0 * System.Math.Cos(originLocation.latitude * System.Math.PI / 180.0)) },
            new { name = "South 50m", latOffset = -50.0 / 111320.0, lonOffset = 0.0 },
            new { name = "West 50m", latOffset = 0.0, lonOffset = -50.0 / (111320.0 * System.Math.Cos(originLocation.latitude * System.Math.PI / 180.0)) }
        };
        
        foreach (var loc in testLocations)
        {
            double lat = originLocation.latitude + loc.latOffset;
            double lon = originLocation.longitude + loc.lonOffset;
            double alt = originLocation.altitude + 5;
            
            var obj = SpawnObjectAtLocation(testCube, lon, lat, alt);
            if (obj != null)
            {
                obj.name = $"TestObject_{loc.name}";
                
                // Add different colors for identification
                var renderer = obj.GetComponent<Renderer>();
                if (renderer != null)
                {
                    if (loc.name.Contains("North")) renderer.material.color = Color.blue;
                    else if (loc.name.Contains("East")) renderer.material.color = Color.green;
                    else if (loc.name.Contains("South")) renderer.material.color = Color.yellow;
                    else if (loc.name.Contains("West")) renderer.material.color = Color.magenta;
                }
            }
        }
        
        // Destroy the template cube
        DestroyImmediate(testCube);
    }
    
    [ContextMenu("Clear Test Objects")]
    public void ClearTestObjects()
    {
        var allObjects = FindObjectsOfType<GameObject>();
        var testObjects = new System.Collections.Generic.List<GameObject>();

        foreach (var obj in allObjects)
        {
            if (obj.name.StartsWith("TestObject_"))
            {
                testObjects.Add(obj);
            }
        }

        foreach (var obj in testObjects)
        {
            DestroyImmediate(obj);
        }
    }
    
    [ContextMenu("Debug Current State")]
    public void DebugCurrentState()
    {
        Debug.Log("=== FIXED GEO-AR DEBUG ===");
        Debug.Log($"Initialized: {isInitialized}");
        
        if (gpsManager != null)
        {
            var currentLoc = gpsManager.CurrentLocation;
            Debug.Log($"Current GPS: {currentLoc.latitude:F8}, {currentLoc.longitude:F8}, {currentLoc.altitude:F2}");
            Debug.Log($"Origin GPS: {originLocation.latitude:F8}, {originLocation.longitude:F8}, {originLocation.altitude:F2}");
            Debug.Log($"Current Heading: {gpsManager.CurrentHeading:F1}°");
        }
        
        if (cesiumReference != null)
        {
            Debug.Log($"Cesium Position: {cesiumReference.transform.position}");
            Debug.Log($"Cesium Rotation: {cesiumReference.transform.rotation.eulerAngles}");
        }
        
        if (xrOrigin != null)
        {
            Debug.Log($"XR Origin Position: {xrOrigin.transform.position}");
            Debug.Log($"XR Origin Rotation: {xrOrigin.transform.rotation.eulerAngles}");
        }
        
        if (arCamera != null)
        {
            Debug.Log($"AR Camera Position: {arCamera.transform.position}");
            Debug.Log($"AR Camera Rotation: {arCamera.transform.rotation.eulerAngles}");
        }
    }
    
    private void Update()
    {
        if (showDebugInfo && debugText != null && isInitialized)
        {
            UpdateDebugUI();
        }
    }
    
    private void UpdateDebugUI()
    {
        if (debugText == null) return;
        
        string info = $"Fixed Geo-AR Status:\n";
        info += $"Initialized: {isInitialized}\n";
        
        if (gpsManager != null && gpsManager.IsGPSRunning)
        {
            var currentLoc = gpsManager.CurrentLocation;
            info += $"GPS: {currentLoc.latitude:F6}, {currentLoc.longitude:F6}\n";
            info += $"Heading: {gpsManager.CurrentHeading:F1}°\n";
        }
        
        info += $"Cesium Rot: {cesiumReference.transform.rotation.eulerAngles}\n";
        info += $"XR Rot: {xrOrigin.transform.rotation.eulerAngles}";
        
        debugText.text = info;
    }
    
    // Public methods for external use
    public void ForceReinitialize()
    {
        isInitialized = false;
        StartCoroutine(InitializeGeoAR());
    }
    
    public Vector3 GetUnityPositionFromGeoCoordinates(double longitude, double latitude, double altitude)
    {
        if (!isInitialized) return Vector3.zero;
        
        // Create temporary object to get Unity position
        var tempObj = new GameObject("TempPositionCalculator");
        tempObj.transform.SetParent(cesiumReference.transform);
        
        var anchor = tempObj.AddComponent<CesiumGlobeAnchor>();
        anchor.SetPositionLongitudeLatitudeHeight(longitude, latitude, altitude);
        
        Vector3 unityPosition = tempObj.transform.position;
        
        DestroyImmediate(tempObj);
        
        return unityPosition;
    }
    
    private void OnDestroy()
    {
        OnSetupComplete = null;
        OnSetupError = null;
    }
}
