using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEngine.XR.ARFoundation;
using ShadowQuality = UnityEngine.ShadowQuality;
using ShadowResolution = UnityEngine.ShadowResolution;

/// <summary>
/// Optimizes performance for mobile AR applications
/// Manages frame rate, memory usage, and battery consumption
/// </summary>
public class MobilePerformanceOptimizer : MonoBehaviour
{
    [Header("Frame Rate Settings")]
    [SerializeField] private int targetFrameRate = 30;
    [SerializeField] private bool adaptiveFrameRate = true;
    [SerializeField] private int minFrameRate = 20;
    [SerializeField] private int maxFrameRate = 60;
    
    [Header("Quality Settings")]
    [SerializeField] private bool autoAdjustQuality = true;
    [SerializeField] private float targetFrameTime = 33.33f; // 30 FPS in milliseconds
    [SerializeField] private int qualityAdjustmentInterval = 60; // frames
    
    [Header("Memory Management")]
    [SerializeField] private bool enableMemoryManagement = true;
    [SerializeField] private long maxMemoryUsageMB = 512;
    [SerializeField] private float memoryCheckInterval = 5f;
    [SerializeField] private bool forceGarbageCollection = false;
    
    [Header("Battery Optimization")]
    [SerializeField] private bool enableBatteryOptimization = true;
    [SerializeField] private float lowBatteryThreshold = 0.2f;
    [SerializeField] private float criticalBatteryThreshold = 0.1f;
    
    [Header("Rendering Optimization")]
    [SerializeField] private bool enableRenderingOptimization = true;
    [SerializeField] private float renderScaleMin = 0.5f;
    [SerializeField] private float renderScaleMax = 1.0f;
    [SerializeField] private bool enableOcclusion = true;
    [SerializeField] private bool enableShadows = false;
    
    [Header("AR Optimization")]
    [SerializeField] private bool optimizeARCamera = true;
    [SerializeField] private bool reduceCameraResolution = false;
    [SerializeField] private Vector2Int lowResCameraSize = new Vector2Int(640, 480);
    
    [Header("Debug")]
    [SerializeField] private bool showPerformanceStats = false;
    [SerializeField] private UnityEngine.UI.Text performanceStatsText;
    [SerializeField] private bool enableDebugLogs = false;
    
    // Performance tracking
    private float[] frameTimeHistory = new float[60];
    private int frameTimeIndex = 0;
    private float averageFrameTime = 0f;
    private int frameCount = 0;
    
    // Quality levels
    public enum PerformanceLevel
    {
        High = 0,
        Medium = 1,
        Low = 2,
        VeryLow = 3
    }
    
    private PerformanceLevel currentPerformanceLevel = PerformanceLevel.High;
    private PerformanceLevel lastPerformanceLevel = PerformanceLevel.High;
    
    // Components
    private ARCameraManager arCameraManager;
    private Camera mainCamera;
    private Light mainLight;
    
    // Battery and thermal state
    private float lastBatteryLevel = 1f;
    private bool isLowBattery = false;
    private bool isCriticalBattery = false;
    private bool isThermalThrottling = false;
    
    // Memory tracking
    private long lastMemoryUsage = 0;
    private Coroutine memoryMonitorCoroutine;
    
    // Events
    public static event Action<PerformanceLevel> OnPerformanceLevelChanged;
    public static event Action<float> OnFrameRateChanged;
    public static event Action<bool> OnLowBatteryStateChanged;
    
    // Singleton
    public static MobilePerformanceOptimizer Instance { get; private set; }
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void Start()
    {
        InitializeOptimizer();
    }
    
    private void InitializeOptimizer()
    {
        // Find components
        arCameraManager = FindObjectOfType<ARCameraManager>();
        mainCamera = Camera.main;
        if (mainCamera == null) mainCamera = FindObjectOfType<Camera>();
        
        mainLight = FindObjectOfType<Light>();
        
        // Set initial frame rate
        Application.targetFrameRate = targetFrameRate;
        
        // Initialize quality settings
        ApplyPerformanceLevel(currentPerformanceLevel);
        
        // Start monitoring
        if (enableMemoryManagement)
        {
            memoryMonitorCoroutine = StartCoroutine(MonitorMemoryUsage());
        }
        
        if (enableDebugLogs)
        {
            Debug.Log($"MobilePerformanceOptimizer initialized. Target FPS: {targetFrameRate}");
        }
    }
    
    private void Update()
    {
        TrackFrameRate();
        
        if (frameCount % qualityAdjustmentInterval == 0)
        {
            if (autoAdjustQuality) AdjustQualityBasedOnPerformance();
            if (enableBatteryOptimization) CheckBatteryStatus();
            CheckThermalState();
        }
        
        if (showPerformanceStats && frameCount % 30 == 0) // Update UI twice per second
        {
            UpdatePerformanceStatsUI();
        }
        
        frameCount++;
    }
    
    private void TrackFrameRate()
    {
        float currentFrameTime = Time.unscaledDeltaTime * 1000f; // Convert to milliseconds
        
        frameTimeHistory[frameTimeIndex] = currentFrameTime;
        frameTimeIndex = (frameTimeIndex + 1) % frameTimeHistory.Length;
        
        // Calculate average frame time
        float totalFrameTime = 0f;
        for (int i = 0; i < frameTimeHistory.Length; i++)
        {
            totalFrameTime += frameTimeHistory[i];
        }
        averageFrameTime = totalFrameTime / frameTimeHistory.Length;
    }
    
    private void AdjustQualityBasedOnPerformance()
    {
        PerformanceLevel newLevel = currentPerformanceLevel;
        
        // Check if we need to reduce quality
        if (averageFrameTime > targetFrameTime * 1.2f) // 20% tolerance
        {
            if (currentPerformanceLevel < PerformanceLevel.VeryLow)
            {
                newLevel = currentPerformanceLevel + 1;
            }
        }
        // Check if we can increase quality
        else if (averageFrameTime < targetFrameTime * 0.8f) // Performance headroom
        {
            if (currentPerformanceLevel > PerformanceLevel.High)
            {
                newLevel = currentPerformanceLevel - 1;
            }
        }
        
        if (newLevel != currentPerformanceLevel)
        {
            SetPerformanceLevel(newLevel);
        }
    }
    
    private void SetPerformanceLevel(PerformanceLevel level)
    {
        lastPerformanceLevel = currentPerformanceLevel;
        currentPerformanceLevel = level;
        
        ApplyPerformanceLevel(level);
        OnPerformanceLevelChanged?.Invoke(level);
        
        if (enableDebugLogs)
        {
            Debug.Log($"Performance level changed to: {level}");
        }
    }
    
    private void ApplyPerformanceLevel(PerformanceLevel level)
    {
        switch (level)
        {
            case PerformanceLevel.High:
                ApplyHighQualitySettings();
                break;
            case PerformanceLevel.Medium:
                ApplyMediumQualitySettings();
                break;
            case PerformanceLevel.Low:
                ApplyLowQualitySettings();
                break;
            case PerformanceLevel.VeryLow:
                ApplyVeryLowQualitySettings();
                break;
        }
    }
    
    private void ApplyHighQualitySettings()
    {
        if (enableRenderingOptimization)
        {
            QualitySettings.shadows = enableShadows ? ShadowQuality.All : ShadowQuality.Disable;
            QualitySettings.shadowResolution = ShadowResolution.High;
            QualitySettings.antiAliasing = 4;
            
            if (mainCamera != null)
            {
#if UNITY_PIPELINE_URP
                var urpData = mainCamera.GetUniversalAdditionalCameraData();
                if (urpData != null)
                {
                    urpData.renderScale = renderScaleMax;
                }
#endif
            }
        }
        
        Application.targetFrameRate = maxFrameRate;
    }
    
    private void ApplyMediumQualitySettings()
    {
        if (enableRenderingOptimization)
        {
            QualitySettings.shadows = ShadowQuality.HardOnly;
            QualitySettings.shadowResolution = ShadowResolution.Medium;
            QualitySettings.antiAliasing = 2;
            
            if (mainCamera != null)
            {
#if UNITY_PIPELINE_URP
                var urpData = mainCamera.GetUniversalAdditionalCameraData();
                if (urpData != null)
                {
                    urpData.renderScale = Mathf.Lerp(renderScaleMin, renderScaleMax, 0.75f);
                }
#endif
            }
        }
        
        Application.targetFrameRate = targetFrameRate;
    }
    
    private void ApplyLowQualitySettings()
    {
        if (enableRenderingOptimization)
        {
            QualitySettings.shadows = ShadowQuality.Disable;
            QualitySettings.antiAliasing = 0;
            
            if (mainCamera != null)
            {
#if UNITY_PIPELINE_URP
                var urpData = mainCamera.GetUniversalAdditionalCameraData();
                if (urpData != null)
                {
                    urpData.renderScale = Mathf.Lerp(renderScaleMin, renderScaleMax, 0.5f);
                }
#endif
            }
        }
        
        Application.targetFrameRate = Mathf.Max(minFrameRate, targetFrameRate - 10);
    }
    
    private void ApplyVeryLowQualitySettings()
    {
        if (enableRenderingOptimization)
        {
            QualitySettings.shadows = ShadowQuality.Disable;
            QualitySettings.antiAliasing = 0;
            QualitySettings.realtimeReflectionProbes = false;
            
            if (mainCamera != null)
            {
#if UNITY_PIPELINE_URP
                var urpData = mainCamera.GetUniversalAdditionalCameraData();
                if (urpData != null)
                {
                    urpData.renderScale = renderScaleMin;
                }
#endif
            }
        }
        
        // Reduce AR camera resolution if enabled
        if (optimizeARCamera && reduceCameraResolution && arCameraManager != null)
        {
            // This would require custom implementation based on AR Foundation version
        }
        
        Application.targetFrameRate = minFrameRate;
    }
    
    private void CheckBatteryStatus()
    {
        float batteryLevel = SystemInfo.batteryLevel;
        
        if (batteryLevel > 0) // Valid battery level
        {
            bool wasLowBattery = isLowBattery;
            bool wasCriticalBattery = isCriticalBattery;
            
            isLowBattery = batteryLevel < lowBatteryThreshold;
            isCriticalBattery = batteryLevel < criticalBatteryThreshold;
            
            if (isLowBattery != wasLowBattery || isCriticalBattery != wasCriticalBattery)
            {
                OnLowBatteryStateChanged?.Invoke(isLowBattery || isCriticalBattery);
                
                if (isCriticalBattery && currentPerformanceLevel < PerformanceLevel.VeryLow)
                {
                    SetPerformanceLevel(PerformanceLevel.VeryLow);
                }
                else if (isLowBattery && currentPerformanceLevel < PerformanceLevel.Low)
                {
                    SetPerformanceLevel(PerformanceLevel.Low);
                }
                
                if (enableDebugLogs)
                {
                    Debug.Log($"Battery status changed: {batteryLevel:P0} (Low: {isLowBattery}, Critical: {isCriticalBattery})");
                }
            }
            
            lastBatteryLevel = batteryLevel;
        }
    }
    
    private void CheckThermalState()
    {
        // Check for thermal throttling indicators
        bool wasThermalThrottling = isThermalThrottling;
        
        // Simple heuristic: if frame rate drops significantly below target
        float currentFPS = 1000f / averageFrameTime;
        isThermalThrottling = currentFPS < (Application.targetFrameRate * 0.7f);
        
        if (isThermalThrottling && !wasThermalThrottling)
        {
            if (currentPerformanceLevel < PerformanceLevel.Low)
            {
                SetPerformanceLevel(PerformanceLevel.Low);
            }
            
            if (enableDebugLogs)
            {
                Debug.Log("Thermal throttling detected, reducing performance level");
            }
        }
    }
    
    private IEnumerator MonitorMemoryUsage()
    {
        while (true)
        {
            yield return new WaitForSeconds(memoryCheckInterval);
            
            long currentMemory = GC.GetTotalMemory(false) / (1024 * 1024); // Convert to MB
            
            if (currentMemory > maxMemoryUsageMB)
            {
                if (forceGarbageCollection)
                {
                    GC.Collect();
                    Resources.UnloadUnusedAssets();
                    
                    if (enableDebugLogs)
                    {
                        Debug.Log($"Memory cleanup performed. Usage: {currentMemory}MB -> {GC.GetTotalMemory(false) / (1024 * 1024)}MB");
                    }
                }
                
                // Reduce quality if memory usage is still high
                if (currentPerformanceLevel < PerformanceLevel.Low)
                {
                    SetPerformanceLevel(currentPerformanceLevel + 1);
                }
            }
            
            lastMemoryUsage = currentMemory;
        }
    }
    
    private void UpdatePerformanceStatsUI()
    {
        if (performanceStatsText == null) return;
        
        float currentFPS = 1000f / averageFrameTime;
        float batteryLevel = SystemInfo.batteryLevel;
        
        string stats = $"FPS: {currentFPS:F1} (Target: {Application.targetFrameRate})\n";
        stats += $"Frame Time: {averageFrameTime:F1}ms\n";
        stats += $"Quality: {currentPerformanceLevel}\n";
        stats += $"Memory: {lastMemoryUsage}MB\n";
        
        if (batteryLevel > 0)
        {
            stats += $"Battery: {batteryLevel:P0}";
            if (isLowBattery) stats += " (LOW)";
            if (isCriticalBattery) stats += " (CRITICAL)";
        }
        
        if (isThermalThrottling)
        {
            stats += "\nTHERMAL THROTTLING";
        }
        
        performanceStatsText.text = stats;
    }
    
    // Public methods
    public void ForcePerformanceLevel(PerformanceLevel level)
    {
        SetPerformanceLevel(level);
    }
    
    public void EnableAutoAdjustment(bool enable)
    {
        autoAdjustQuality = enable;
    }
    
    public float GetCurrentFPS()
    {
        return 1000f / averageFrameTime;
    }
    
    public PerformanceLevel GetCurrentPerformanceLevel()
    {
        return currentPerformanceLevel;
    }
    
    public void ForceGarbageCollection()
    {
        GC.Collect();
        Resources.UnloadUnusedAssets();
        
        if (enableDebugLogs)
        {
            Debug.Log("Manual garbage collection performed");
        }
    }
    
    private void OnDestroy()
    {
        if (memoryMonitorCoroutine != null)
        {
            StopCoroutine(memoryMonitorCoroutine);
        }
        
        // Clear events
        OnPerformanceLevelChanged = null;
        OnFrameRateChanged = null;
        OnLowBatteryStateChanged = null;
    }
}