using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Demo script for testing the Fixed Geo-AR system
/// Provides UI controls to spawn and test objects at geographic locations
/// </summary>
public class FixedGeoARDemo : MonoBehaviour
{
    [Header("UI References")]
    public Button initializeButton;
    public Button spawnTestObjectsButton;
    public Button clearObjectsButton;
    public Button debugStateButton;
    public Button spawnNorthButton;
    public Button spawnEastButton;
    public Button spawnSouthButton;
    public Button spawnWestButton;
    public Text statusText;
    public Text gpsText;
    
    [Header("Test Prefabs")]
    public GameObject[] testPrefabs;
    
    [Header("Settings")]
    public float spawnDistance = 50f; // meters
    public float spawnHeight = 5f; // meters above ground
    
    private FixedGeoARSetup geoARSetup;
    private GPSCompassManager gpsManager;
    private int spawnedObjectCount = 0;
    
    private void Start()
    {
        SetupUI();
        FindComponents();
        SubscribeToEvents();
    }
    
    private void SetupUI()
    {
        if (initializeButton != null)
            initializeButton.onClick.AddListener(OnInitializeClicked);
        
        if (spawnTestObjectsButton != null)
            spawnTestObjectsButton.onClick.AddListener(OnSpawnTestObjectsClicked);
        
        if (clearObjectsButton != null)
            clearObjectsButton.onClick.AddListener(OnClearObjectsClicked);
        
        if (debugStateButton != null)
            debugStateButton.onClick.AddListener(OnDebugStateClicked);
        
        if (spawnNorthButton != null)
            spawnNorthButton.onClick.AddListener(() => SpawnObjectInDirection("North", 0f));
        
        if (spawnEastButton != null)
            spawnEastButton.onClick.AddListener(() => SpawnObjectInDirection("East", 90f));
        
        if (spawnSouthButton != null)
            spawnSouthButton.onClick.AddListener(() => SpawnObjectInDirection("South", 180f));
        
        if (spawnWestButton != null)
            spawnWestButton.onClick.AddListener(() => SpawnObjectInDirection("West", 270f));
        
        UpdateStatusText("Ready to initialize");
        UpdateButtonStates(false);
    }
    
    private void FindComponents()
    {
        geoARSetup = FindObjectOfType<FixedGeoARSetup>();
        if (geoARSetup == null)
        {
            UpdateStatusText("FixedGeoARSetup not found in scene!");
        }
        
        gpsManager = GPSCompassManager.Instance;
    }
    
    private void SubscribeToEvents()
    {
        FixedGeoARSetup.OnSetupComplete += OnSetupComplete;
        FixedGeoARSetup.OnSetupError += OnSetupError;
        
        if (gpsManager != null)
        {
            GPSCompassManager.OnLocationUpdated += OnLocationUpdated;
        }
    }
    
    private void OnSetupComplete()
    {
        UpdateStatusText("Geo-AR setup complete! Ready to spawn objects.");
        UpdateButtonStates(true);
    }
    
    private void OnSetupError(string error)
    {
        UpdateStatusText($"Setup error: {error}");
        UpdateButtonStates(false);
    }
    
    private void OnLocationUpdated(LocationInfo location)
    {
        UpdateGPSText(location);
    }
    
    private void UpdateStatusText(string message)
    {
        if (statusText != null)
        {
            statusText.text = $"[{System.DateTime.Now:HH:mm:ss}] {message}";
        }
        Debug.Log($"FixedGeoARDemo: {message}");
    }
    
    private void UpdateGPSText(LocationInfo location)
    {
        if (gpsText != null && gpsManager != null)
        {
            string gpsInfo = $"GPS: {location.latitude:F6}, {location.longitude:F6}\n";
            gpsInfo += $"Alt: {location.altitude:F1}m, Acc: {location.horizontalAccuracy:F1}m\n";
            gpsInfo += $"Heading: {gpsManager.CurrentHeading:F1}°";
            gpsText.text = gpsInfo;
        }
    }
    
    private void UpdateButtonStates(bool setupComplete)
    {
        if (spawnTestObjectsButton != null)
            spawnTestObjectsButton.interactable = setupComplete;
        
        if (clearObjectsButton != null)
            clearObjectsButton.interactable = setupComplete;
        
        if (spawnNorthButton != null)
            spawnNorthButton.interactable = setupComplete;
        
        if (spawnEastButton != null)
            spawnEastButton.interactable = setupComplete;
        
        if (spawnSouthButton != null)
            spawnSouthButton.interactable = setupComplete;
        
        if (spawnWestButton != null)
            spawnWestButton.interactable = setupComplete;
    }
    
    // UI Button Handlers
    private void OnInitializeClicked()
    {
        if (geoARSetup != null)
        {
            UpdateStatusText("Initializing Geo-AR system...");
            geoARSetup.ForceReinitialize();
        }
        else
        {
            UpdateStatusText("FixedGeoARSetup component not found!");
        }
    }
    
    private void OnSpawnTestObjectsClicked()
    {
        if (geoARSetup != null && geoARSetup.IsInitialized)
        {
            geoARSetup.TestSpawnObjects();
            UpdateStatusText("Test objects spawned!");
        }
        else
        {
            UpdateStatusText("System not initialized yet!");
        }
    }
    
    private void OnClearObjectsClicked()
    {
        if (geoARSetup != null)
        {
            geoARSetup.ClearTestObjects();
            spawnedObjectCount = 0;
            UpdateStatusText("Objects cleared!");
        }
    }
    
    private void OnDebugStateClicked()
    {
        if (geoARSetup != null)
        {
            geoARSetup.DebugCurrentState();
            UpdateStatusText("Debug info printed to console");
        }
    }
    
    private void SpawnObjectInDirection(string direction, float bearing)
    {
        if (geoARSetup == null || !geoARSetup.IsInitialized)
        {
            UpdateStatusText("System not initialized!");
            return;
        }
        
        if (gpsManager == null || !gpsManager.IsGPSRunning)
        {
            UpdateStatusText("GPS not available!");
            return;
        }
        
        // Get current location
        var currentLocation = gpsManager.CurrentLocation;
        
        // Calculate target coordinates
        double[] targetCoords = CalculateTargetCoordinates(
            currentLocation.latitude, 
            currentLocation.longitude, 
            spawnDistance, 
            bearing
        );
        
        // Choose prefab
        GameObject prefab = GetRandomTestPrefab();
        if (prefab == null)
        {
            // Create default cube if no prefabs available
            prefab = GameObject.CreatePrimitive(PrimitiveType.Cube);
            prefab.transform.localScale = Vector3.one * 2f;
            
            // Set color based on direction
            var renderer = prefab.GetComponent<Renderer>();
            if (renderer != null)
            {
                switch (direction)
                {
                    case "North": renderer.material.color = Color.blue; break;
                    case "East": renderer.material.color = Color.green; break;
                    case "South": renderer.material.color = Color.yellow; break;
                    case "West": renderer.material.color = Color.magenta; break;
                }
            }
        }
        
        // Spawn object
        var spawnedObject = geoARSetup.SpawnObjectAtLocation(
            prefab, 
            targetCoords[1], // longitude
            targetCoords[0], // latitude
            currentLocation.altitude + spawnHeight
        );
        
        if (spawnedObject != null)
        {
            spawnedObject.name = $"DirectionalObject_{direction}_{spawnedObjectCount++}";
            UpdateStatusText($"Spawned object {spawnDistance}m {direction}");
            
            // If we created a default cube, destroy the template
            if (testPrefabs == null || testPrefabs.Length == 0)
            {
                DestroyImmediate(prefab);
            }
        }
        else
        {
            UpdateStatusText($"Failed to spawn object {direction}");
        }
    }
    
    private double[] CalculateTargetCoordinates(double lat, double lon, float distanceMeters, float bearingDegrees)
    {
        // Convert to radians
        double latRad = lat * Mathf.Deg2Rad;
        double lonRad = lon * Mathf.Deg2Rad;
        double bearingRad = bearingDegrees * Mathf.Deg2Rad;
        
        // Earth radius in meters
        double earthRadius = 6371000;
        
        // Calculate new coordinates
        double newLatRad = System.Math.Asin(
            System.Math.Sin(latRad) * System.Math.Cos(distanceMeters / earthRadius) +
            System.Math.Cos(latRad) * System.Math.Sin(distanceMeters / earthRadius) * System.Math.Cos(bearingRad)
        );
        
        double newLonRad = lonRad + System.Math.Atan2(
            System.Math.Sin(bearingRad) * System.Math.Sin(distanceMeters / earthRadius) * System.Math.Cos(latRad),
            System.Math.Cos(distanceMeters / earthRadius) - System.Math.Sin(latRad) * System.Math.Sin(newLatRad)
        );
        
        // Convert back to degrees
        double newLat = newLatRad * Mathf.Rad2Deg;
        double newLon = newLonRad * Mathf.Rad2Deg;
        
        return new double[] { newLat, newLon };
    }
    
    private GameObject GetRandomTestPrefab()
    {
        if (testPrefabs == null || testPrefabs.Length == 0)
            return null;
        
        return testPrefabs[Random.Range(0, testPrefabs.Length)];
    }
    
    private void Update()
    {
        // Update GPS info periodically
        if (Time.frameCount % 30 == 0 && gpsManager != null && gpsManager.IsGPSRunning)
        {
            UpdateGPSText(gpsManager.CurrentLocation);
        }
    }
    
    private void OnDestroy()
    {
        // Unsubscribe from events
        FixedGeoARSetup.OnSetupComplete -= OnSetupComplete;
        FixedGeoARSetup.OnSetupError -= OnSetupError;
        
        if (gpsManager != null)
        {
            GPSCompassManager.OnLocationUpdated -= OnLocationUpdated;
        }
    }
    
    // Public methods for external use
    public void SpawnObjectAtCurrentLocation()
    {
        if (geoARSetup == null || !geoARSetup.IsInitialized || gpsManager == null)
            return;
        
        var location = gpsManager.CurrentLocation;
        var prefab = GetRandomTestPrefab();
        
        if (prefab != null)
        {
            geoARSetup.SpawnObjectAtLocation(
                prefab,
                location.longitude,
                location.latitude,
                location.altitude + spawnHeight
            );
        }
    }
    
    public void SetSpawnDistance(float distance)
    {
        spawnDistance = distance;
        UpdateStatusText($"Spawn distance set to {distance}m");
    }
    
    public void SetSpawnHeight(float height)
    {
        spawnHeight = height;
        UpdateStatusText($"Spawn height set to {height}m");
    }
}
