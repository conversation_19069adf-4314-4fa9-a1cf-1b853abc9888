using System;
using System.Collections;
using UnityEngine;

/// <summary>
/// Centralized GPS and Compass management system for AR applications
/// Provides accurate location tracking with battery optimization and error handling
/// </summary>
public class GPSCompassManager : MonoBehaviour
{
    [Header("GPS Settings")]
    [SerializeField] private float desiredAccuracyInMeters = 5f;
    [SerializeField] private float updateDistanceInMeters = 1f;
    [SerializeField] private int maxInitWaitTime = 30;
    [SerializeField] private float locationUpdateInterval = 0.5f;
    
    [Header("Compass Settings")]
    [SerializeField] private bool enableCompass = true;
    [SerializeField] private float compassUpdateInterval = 0.1f;
    [SerializeField] private float headingSmoothingFactor = 0.2f;
    [SerializeField] private float headingChangeThreshold = 2f;
    
    [Header("Battery Optimization")]
    [SerializeField] private bool enableBatteryOptimization = true;
    [SerializeField] private float lowBatteryThreshold = 0.2f;
    [SerializeField] private float reducedUpdateInterval = 2f;
    
    [Header("Debug")]
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private bool showLocationInUI = false;
    [SerializeField] private UnityEngine.UI.Text debugText;
    
    // Events
    public static event Action<LocationInfo> OnLocationUpdated;
    public static event Action<float> OnHeadingUpdated;
    public static event Action<string> OnGPSError;
    public static event Action OnGPSInitialized;
    public static event Action<float> OnLocationAccuracyChanged;
    
    // Properties
    public bool IsGPSRunning => Input.location.status == LocationServiceStatus.Running;
    public bool IsCompassEnabled => Input.compass.enabled;
    public LocationInfo CurrentLocation { get; private set; }
    public float CurrentHeading { get; private set; }
    public float LocationAccuracy { get; private set; }
    public bool IsInitialized { get; private set; }
    
    // Private fields
    private Coroutine gpsTrackingCoroutine;
    private Coroutine compassTrackingCoroutine;
    private float lastLocationUpdate;
    private float lastCompassUpdate;
    private float smoothedHeading;
    private bool isLowBattery;
    private LocationInfo lastValidLocation;
    
    // Singleton pattern
    public static GPSCompassManager Instance { get; private set; }
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void Start()
    {
        StartCoroutine(InitializeServices());
    }
    
    private IEnumerator InitializeServices()
    {
        if (enableDebugLogs) Debug.Log("Initializing GPS and Compass services...");
        
        // Check battery level
        CheckBatteryLevel();
        
        // Request location permission
        yield return StartCoroutine(RequestLocationPermission());
        
        // Initialize GPS
        yield return StartCoroutine(InitializeGPS());
        
        // Initialize Compass
        if (enableCompass)
        {
            InitializeCompass();
        }
        
        // Start tracking coroutines
        StartTracking();
        
        IsInitialized = true;
        OnGPSInitialized?.Invoke();
        
        if (enableDebugLogs) Debug.Log("GPS and Compass services initialized successfully!");
    }
    
    private IEnumerator RequestLocationPermission()
    {
#if UNITY_ANDROID
        if (!UnityEngine.Android.Permission.HasUserAuthorizedPermission(UnityEngine.Android.Permission.FineLocation))
        {
            UnityEngine.Android.Permission.RequestUserPermission(UnityEngine.Android.Permission.FineLocation);
            
            float timeout = 15f;
            while (!UnityEngine.Android.Permission.HasUserAuthorizedPermission(UnityEngine.Android.Permission.FineLocation) && timeout > 0)
            {
                yield return new WaitForSeconds(0.1f);
                timeout -= 0.1f;
            }
            
            if (!UnityEngine.Android.Permission.HasUserAuthorizedPermission(UnityEngine.Android.Permission.FineLocation))
            {
                OnGPSError?.Invoke("Location permission denied by user");
                yield break;
            }
        }
#endif
        yield return null;
    }
    
    private IEnumerator InitializeGPS()
    {
        if (!Input.location.isEnabledByUser)
        {
            OnGPSError?.Invoke("Location services are disabled by user");
            yield break;
        }
        
        // Start location service
        Input.location.Start(desiredAccuracyInMeters, updateDistanceInMeters);
        
        // Wait for GPS to initialize
        int waitTime = 0;
        while (Input.location.status == LocationServiceStatus.Initializing && waitTime < maxInitWaitTime)
        {
            if (enableDebugLogs && waitTime % 5 == 0)
            {
                Debug.Log($"Waiting for GPS initialization... {waitTime}/{maxInitWaitTime}s");
            }
            yield return new WaitForSeconds(1);
            waitTime++;
        }
        
        // Check final status
        if (Input.location.status != LocationServiceStatus.Running)
        {
            string error = $"GPS failed to initialize. Status: {Input.location.status}";
            OnGPSError?.Invoke(error);
            if (enableDebugLogs) Debug.LogError(error);
            yield break;
        }
        
        // Get initial location
        CurrentLocation = Input.location.lastData;
        lastValidLocation = CurrentLocation;
        LocationAccuracy = CurrentLocation.horizontalAccuracy;
        
        if (enableDebugLogs)
        {
            Debug.Log($"GPS initialized. Location: {CurrentLocation.latitude:F6}, {CurrentLocation.longitude:F6}, Accuracy: {LocationAccuracy:F1}m");
        }
    }
    
    private void InitializeCompass()
    {
        Input.compass.enabled = true;
        
        // Wait a moment for compass to stabilize
        StartCoroutine(WaitForCompassStabilization());
    }
    
    private IEnumerator WaitForCompassStabilization()
    {
        yield return new WaitForSeconds(2f);
        
        CurrentHeading = Input.compass.trueHeading;
        smoothedHeading = CurrentHeading;
        
        if (enableDebugLogs)
        {
            Debug.Log($"Compass initialized. Heading: {CurrentHeading:F1}°, Accuracy: {Input.compass.headingAccuracy:F1}°");
        }
    }
    
    private void StartTracking()
    {
        if (IsGPSRunning)
        {
            gpsTrackingCoroutine = StartCoroutine(TrackLocation());
        }
        
        if (enableCompass && IsCompassEnabled)
        {
            compassTrackingCoroutine = StartCoroutine(TrackCompass());
        }
    }
    
    private IEnumerator TrackLocation()
    {
        while (IsGPSRunning)
        {
            float updateInterval = isLowBattery ? reducedUpdateInterval : locationUpdateInterval;
            
            if (Time.time - lastLocationUpdate >= updateInterval)
            {
                UpdateLocation();
                lastLocationUpdate = Time.time;
            }
            
            yield return new WaitForSeconds(0.1f);
        }
    }
    
    private IEnumerator TrackCompass()
    {
        while (IsCompassEnabled)
        {
            float updateInterval = isLowBattery ? compassUpdateInterval * 2 : compassUpdateInterval;
            
            if (Time.time - lastCompassUpdate >= updateInterval)
            {
                UpdateCompass();
                lastCompassUpdate = Time.time;
            }
            
            yield return new WaitForSeconds(0.05f);
        }
    }
    
    private void UpdateLocation()
    {
        var newLocation = Input.location.lastData;
        
        // Validate location data
        if (IsValidLocation(newLocation))
        {
            CurrentLocation = newLocation;
            lastValidLocation = newLocation;
            LocationAccuracy = newLocation.horizontalAccuracy;
            
            OnLocationUpdated?.Invoke(CurrentLocation);
            OnLocationAccuracyChanged?.Invoke(LocationAccuracy);
            
            if (enableDebugLogs)
            {
                Debug.Log($"Location updated: {CurrentLocation.latitude:F6}, {CurrentLocation.longitude:F6}, Accuracy: {LocationAccuracy:F1}m");
            }
        }
        else if (enableDebugLogs)
        {
            Debug.LogWarning($"Invalid location data received: {newLocation.latitude}, {newLocation.longitude}");
        }
        
        UpdateDebugUI();
    }
    
    private void UpdateCompass()
    {
        float rawHeading = Input.compass.trueHeading;
        
        // Apply smoothing
        float angleDiff = Mathf.DeltaAngle(smoothedHeading, rawHeading);
        smoothedHeading += angleDiff * headingSmoothingFactor;
        
        // Normalize to 0-360 range
        if (smoothedHeading < 0) smoothedHeading += 360f;
        if (smoothedHeading >= 360) smoothedHeading -= 360f;
        
        // Check if heading changed significantly
        if (Mathf.Abs(Mathf.DeltaAngle(CurrentHeading, smoothedHeading)) >= headingChangeThreshold)
        {
            CurrentHeading = smoothedHeading;
            OnHeadingUpdated?.Invoke(CurrentHeading);
            
            if (enableDebugLogs)
            {
                Debug.Log($"Heading updated: {CurrentHeading:F1}°, Accuracy: {Input.compass.headingAccuracy:F1}°");
            }
        }
        
        UpdateDebugUI();
    }
    
    private bool IsValidLocation(LocationInfo location)
    {
        // Check for obviously invalid coordinates
        if (location.latitude == 0 && location.longitude == 0) return false;
        if (Mathf.Abs(location.latitude) > 90 || Mathf.Abs(location.longitude) > 180) return false;
        
        // Check accuracy
        if (location.horizontalAccuracy < 0 || location.horizontalAccuracy > 100) return false;
        
        return true;
    }
    
    private void CheckBatteryLevel()
    {
        if (!enableBatteryOptimization) return;
        
        float batteryLevel = SystemInfo.batteryLevel;
        isLowBattery = batteryLevel > 0 && batteryLevel < lowBatteryThreshold;
        
        if (isLowBattery && enableDebugLogs)
        {
            Debug.Log($"Low battery detected ({batteryLevel:P0}). Reducing update frequency.");
        }
    }
    
    private void UpdateDebugUI()
    {
        if (!showLocationInUI || debugText == null) return;
        
        string debugInfo = $"GPS: {(IsGPSRunning ? "ON" : "OFF")}\n";
        debugInfo += $"Compass: {(IsCompassEnabled ? "ON" : "OFF")}\n";
        
        if (IsGPSRunning)
        {
            debugInfo += $"Lat: {CurrentLocation.latitude:F6}\n";
            debugInfo += $"Lon: {CurrentLocation.longitude:F6}\n";
            debugInfo += $"Alt: {CurrentLocation.altitude:F1}m\n";
            debugInfo += $"Accuracy: {LocationAccuracy:F1}m\n";
        }
        
        if (IsCompassEnabled)
        {
            debugInfo += $"Heading: {CurrentHeading:F1}°\n";
            debugInfo += $"Compass Acc: {Input.compass.headingAccuracy:F1}°";
        }
        
        debugText.text = debugInfo;
    }
    
    // Public methods
    public void StopTracking()
    {
        if (gpsTrackingCoroutine != null)
        {
            StopCoroutine(gpsTrackingCoroutine);
            gpsTrackingCoroutine = null;
        }
        
        if (compassTrackingCoroutine != null)
        {
            StopCoroutine(compassTrackingCoroutine);
            compassTrackingCoroutine = null;
        }
        
        Input.location.Stop();
        Input.compass.enabled = false;
        IsInitialized = false;
    }
    
    public void RestartTracking()
    {
        StopTracking();
        StartCoroutine(InitializeServices());
    }
    
    public float GetDistanceTo(double latitude, double longitude)
    {
        if (!IsGPSRunning) return float.MaxValue;
        
        return CalculateDistance(CurrentLocation.latitude, CurrentLocation.longitude, latitude, longitude);
    }
    
    public float GetBearingTo(double latitude, double longitude)
    {
        if (!IsGPSRunning) return 0f;
        
        return CalculateBearing(CurrentLocation.latitude, CurrentLocation.longitude, latitude, longitude);
    }
    
    private float CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371000; // Earth radius in meters
        double dLat = (lat2 - lat1) * Mathf.Deg2Rad;
        double dLon = (lon2 - lon1) * Mathf.Deg2Rad;
        
        double a = Mathf.Sin((float)dLat / 2) * Mathf.Sin((float)dLat / 2) +
                   Mathf.Cos((float)(lat1 * Mathf.Deg2Rad)) * Mathf.Cos((float)(lat2 * Mathf.Deg2Rad)) *
                   Mathf.Sin((float)dLon / 2) * Mathf.Sin((float)dLon / 2);
        
        double c = 2 * Mathf.Atan2(Mathf.Sqrt((float)a), Mathf.Sqrt((float)(1 - a)));
        return (float)(R * c);
    }
    
    private float CalculateBearing(double lat1, double lon1, double lat2, double lon2)
    {
        double dLon = (lon2 - lon1) * Mathf.Deg2Rad;
        double lat1Rad = lat1 * Mathf.Deg2Rad;
        double lat2Rad = lat2 * Mathf.Deg2Rad;
        
        double y = Mathf.Sin((float)dLon) * Mathf.Cos((float)lat2Rad);
        double x = Mathf.Cos((float)lat1Rad) * Mathf.Sin((float)lat2Rad) -
                   Mathf.Sin((float)lat1Rad) * Mathf.Cos((float)lat2Rad) * Mathf.Cos((float)dLon);
        
        double bearing = Mathf.Atan2((float)y, (float)x) * Mathf.Rad2Deg;
        return (float)((bearing + 360) % 360);
    }
    
    private void Update()
    {
        // Check battery level periodically
        if (enableBatteryOptimization && Time.frameCount % 300 == 0) // Every ~5 seconds at 60fps
        {
            CheckBatteryLevel();
        }
    }
    
    private void OnDestroy()
    {
        StopTracking();
        
        // Clear events
        OnLocationUpdated = null;
        OnHeadingUpdated = null;
        OnGPSError = null;
        OnGPSInitialized = null;
        OnLocationAccuracyChanged = null;
    }
}
