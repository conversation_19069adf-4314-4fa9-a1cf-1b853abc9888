using System;
using UnityEngine;
using CesiumForUnity;

public class SpawnAnchoredObject : MonoBehaviour
{
    public CesiumGeoreference cesiumReference;
    public GameObject prefab;
    [Tooltip("Tọa độ đích muốn neo (lon, lat, alt)")]
    public double targetLon, targetLat, targetAlt; 

    [Obsolete("Obsolete")]
    void Start()
    {
        // Instantiate trong Cesium world
        GameObject obj = Instantiate(prefab, cesiumReference.transform);
        /*obj.transform.parent = cesiumReference.transform;*/
        var anchor = obj.AddComponent<CesiumGlobeAnchor>();
        anchor.SetPositionLongitudeLatitudeHeight(
            targetLon, targetLat, targetAlt);
        anchor.adjustOrientationForGlobeWhenMoving = true;
        anchor.detectTransformChanges = false;
    }
}