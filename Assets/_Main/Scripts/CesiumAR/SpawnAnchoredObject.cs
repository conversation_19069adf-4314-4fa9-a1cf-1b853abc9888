using System;
using System.Collections.Generic;
using UnityEngine;
using CesiumForUnity;

[System.Serializable]
public class GeoAnchoredObject
{
    [Header("Object Settings")]
    public GameObject prefab;
    public string objectId;
    [Tooltip("Geographic coordinates (longitude, latitude, altitude)")]
    public double longitude, latitude, altitude;

    [Header("Visibility Settings")]
    [Tooltip("Maximum distance to show this object (meters)")]
    public float maxVisibilityDistance = 1000f;
    [Tooltip("Minimum distance to show this object (meters)")]
    public float minVisibilityDistance = 0f;

    [Header("LOD Settings")]
    public bool useLOD = false;
    public GameObject[] lodPrefabs; // LOD0 (highest detail) to LODn (lowest detail)
    public float[] lodDistances; // Distance thresholds for each LOD

    [Header("Runtime Info")]
    [SerializeField] private GameObject spawnedObject;
    [SerializeField] private CesiumGlobeAnchor anchor;
    [SerializeField] private float distanceFromPlayer;
    [SerializeField] private bool isVisible;
    [SerializeField] private int currentLOD = -1;

    // Properties
    public GameObject SpawnedObject => spawnedObject;
    public CesiumGlobeAnchor Anchor => anchor;
    public float DistanceFromPlayer => distanceFromPlayer;
    public bool IsVisible => isVisible;
    public int CurrentLOD => currentLOD;

    public void SetSpawnedObject(GameObject obj, CesiumGlobeAnchor anch)
    {
        spawnedObject = obj;
        anchor = anch;
    }

    public void UpdateDistance(float distance)
    {
        distanceFromPlayer = distance;
    }

    public void SetVisibility(bool visible)
    {
        isVisible = visible;
        if (spawnedObject != null)
        {
            spawnedObject.SetActive(visible);
        }
    }

    public void SetLOD(int lodLevel)
    {
        currentLOD = lodLevel;
    }
}

public class SpawnAnchoredObject : MonoBehaviour
{
    [Header("Cesium Reference")]
    public CesiumGeoreference cesiumReference;

    [Header("Geo-Anchored Objects")]
    public List<GeoAnchoredObject> geoObjects = new List<GeoAnchoredObject>();

    [Header("Performance Settings")]
    [SerializeField] private bool enableDistanceCulling = true;
    [SerializeField] private bool enableLOD = true;
    [SerializeField] private float updateInterval = 1f;
    [SerializeField] private int maxVisibleObjects = 50;

    [Header("Debug")]
    [SerializeField] private bool enableDebugLogs = false;
    [SerializeField] private bool showGizmos = true;

    // Private fields
    private Camera playerCamera;
    private Transform playerTransform;
    private List<GeoAnchoredObject> spawnedObjects = new List<GeoAnchoredObject>();
    private float lastUpdateTime;

    // Events
    public static event Action<GeoAnchoredObject> OnObjectSpawned;
    public static event Action<GeoAnchoredObject> OnObjectDestroyed;

    private void Start()
    {
        InitializeSystem();
        SpawnAllObjects();
    }

    private void InitializeSystem()
    {
        // Find player camera (AR Camera)
        playerCamera = Camera.main;
        if (playerCamera == null)
        {
            playerCamera = FindObjectOfType<Camera>();
        }

        if (playerCamera != null)
        {
            playerTransform = playerCamera.transform;
        }
        else
        {
            Debug.LogError("No camera found for distance calculations!");
        }

        // Subscribe to location updates
        SimpleGeoARSetup.OnLocationUpdated += OnPlayerLocationUpdated;

        if (enableDebugLogs)
        {
            Debug.Log($"SpawnAnchoredObject initialized with {geoObjects.Count} objects to spawn");
        }
    }

    private void SpawnAllObjects()
    {
        if (cesiumReference == null)
        {
            Debug.LogError("CesiumGeoreference not assigned!");
            return;
        }

        foreach (var geoObj in geoObjects)
        {
            SpawnObject(geoObj);
        }
    }

    private void SpawnObject(GeoAnchoredObject geoObj)
    {
        if (geoObj.prefab == null)
        {
            Debug.LogWarning($"Prefab not assigned for object {geoObj.objectId}");
            return;
        }

        // Choose appropriate prefab (LOD0 or main prefab)
        GameObject prefabToSpawn = geoObj.useLOD && geoObj.lodPrefabs.Length > 0
            ? geoObj.lodPrefabs[0]
            : geoObj.prefab;

        // Instantiate object
        GameObject spawnedObj = Instantiate(prefabToSpawn, cesiumReference.transform);
        spawnedObj.name = $"GeoObject_{geoObj.objectId}";

        // Add and configure CesiumGlobeAnchor
        var anchor = spawnedObj.GetComponent<CesiumGlobeAnchor>();
        if (anchor == null)
        {
            anchor = spawnedObj.AddComponent<CesiumGlobeAnchor>();
        }

        anchor.SetPositionLongitudeLatitudeHeight(
            geoObj.longitude, geoObj.latitude, geoObj.altitude);
        anchor.adjustOrientationForGlobeWhenMoving = true;
        anchor.detectTransformChanges = false;

        // Store references
        geoObj.SetSpawnedObject(spawnedObj, anchor);
        spawnedObjects.Add(geoObj);

        // Initial visibility check
        UpdateObjectVisibility(geoObj);

        // Fire event
        OnObjectSpawned?.Invoke(geoObj);

        if (enableDebugLogs)
        {
            Debug.Log($"Spawned object {geoObj.objectId} at {geoObj.longitude:F6}, {geoObj.latitude:F6}, {geoObj.altitude:F1}");
        }
    }

    private void Update()
    {
        if (Time.time - lastUpdateTime >= updateInterval)
        {
            UpdateAllObjects();
            lastUpdateTime = Time.time;
        }
    }

    private void UpdateAllObjects()
    {
        if (playerTransform == null) return;

        // Sort objects by distance for LOD prioritization
        spawnedObjects.Sort((a, b) => a.DistanceFromPlayer.CompareTo(b.DistanceFromPlayer));

        int visibleCount = 0;

        foreach (var geoObj in spawnedObjects)
        {
            if (geoObj.SpawnedObject == null) continue;

            // Update distance
            float distance = CalculateDistanceToObject(geoObj);
            geoObj.UpdateDistance(distance);

            // Update visibility
            bool shouldBeVisible = ShouldObjectBeVisible(geoObj, visibleCount);

            if (shouldBeVisible)
            {
                visibleCount++;

                // Update LOD if enabled
                if (enableLOD && geoObj.useLOD)
                {
                    UpdateObjectLOD(geoObj);
                }

                geoObj.SetVisibility(true);
            }
            else
            {
                geoObj.SetVisibility(false);
            }
        }

        if (enableDebugLogs && visibleCount > 0)
        {
            Debug.Log($"Visible objects: {visibleCount}/{spawnedObjects.Count}");
        }
    }

    private float CalculateDistanceToObject(GeoAnchoredObject geoObj)
    {
        if (playerTransform == null || geoObj.SpawnedObject == null) return float.MaxValue;

        return Vector3.Distance(playerTransform.position, geoObj.SpawnedObject.transform.position);
    }

    private bool ShouldObjectBeVisible(GeoAnchoredObject geoObj, int currentVisibleCount)
    {
        if (!enableDistanceCulling) return true;
        if (currentVisibleCount >= maxVisibleObjects) return false;

        float distance = geoObj.DistanceFromPlayer;

        return distance >= geoObj.minVisibilityDistance &&
               distance <= geoObj.maxVisibilityDistance;
    }

    private void UpdateObjectVisibility(GeoAnchoredObject geoObj)
    {
        float distance = CalculateDistanceToObject(geoObj);
        geoObj.UpdateDistance(distance);

        bool shouldBeVisible = ShouldObjectBeVisible(geoObj, 0);
        geoObj.SetVisibility(shouldBeVisible);
    }

    private void UpdateObjectLOD(GeoAnchoredObject geoObj)
    {
        if (!geoObj.useLOD || geoObj.lodPrefabs.Length == 0 || geoObj.lodDistances.Length == 0)
            return;

        float distance = geoObj.DistanceFromPlayer;
        int newLOD = 0;

        // Determine appropriate LOD level
        for (int i = 0; i < geoObj.lodDistances.Length; i++)
        {
            if (distance > geoObj.lodDistances[i])
            {
                newLOD = i + 1;
            }
            else
            {
                break;
            }
        }

        // Clamp to available LOD levels
        newLOD = Mathf.Clamp(newLOD, 0, geoObj.lodPrefabs.Length - 1);

        // Update LOD if changed
        if (newLOD != geoObj.CurrentLOD)
        {
            SwapObjectLOD(geoObj, newLOD);
            geoObj.SetLOD(newLOD);

            if (enableDebugLogs)
            {
                Debug.Log($"Object {geoObj.objectId} LOD changed to {newLOD} (distance: {distance:F1}m)");
            }
        }
    }

    private void SwapObjectLOD(GeoAnchoredObject geoObj, int newLOD)
    {
        if (geoObj.SpawnedObject == null || newLOD >= geoObj.lodPrefabs.Length) return;

        // Store current transform data
        Vector3 position = geoObj.SpawnedObject.transform.position;
        Quaternion rotation = geoObj.SpawnedObject.transform.rotation;
        Vector3 scale = geoObj.SpawnedObject.transform.localScale;
        bool wasActive = geoObj.SpawnedObject.activeSelf;

        // Destroy current object
        DestroyImmediate(geoObj.SpawnedObject);

        // Instantiate new LOD
        GameObject newObj = Instantiate(geoObj.lodPrefabs[newLOD], cesiumReference.transform);
        newObj.name = $"GeoObject_{geoObj.objectId}_LOD{newLOD}";

        // Restore transform
        newObj.transform.position = position;
        newObj.transform.rotation = rotation;
        newObj.transform.localScale = scale;
        newObj.SetActive(wasActive);

        // Update anchor
        var anchor = newObj.GetComponent<CesiumGlobeAnchor>();
        if (anchor == null)
        {
            anchor = newObj.AddComponent<CesiumGlobeAnchor>();
        }

        anchor.SetPositionLongitudeLatitudeHeight(
            geoObj.longitude, geoObj.latitude, geoObj.altitude);
        anchor.adjustOrientationForGlobeWhenMoving = true;
        anchor.detectTransformChanges = false;

        // Update references
        geoObj.SetSpawnedObject(newObj, anchor);
    }

    private void OnPlayerLocationUpdated(LocationInfo locationInfo)
    {
        // Trigger immediate update when player location changes significantly
        if (enableDistanceCulling)
        {
            UpdateAllObjects();
        }
    }

    // Public methods for runtime management
    public void AddGeoObject(GeoAnchoredObject geoObj)
    {
        if (!geoObjects.Contains(geoObj))
        {
            geoObjects.Add(geoObj);
            if (Application.isPlaying)
            {
                SpawnObject(geoObj);
            }
        }
    }

    public void RemoveGeoObject(string objectId)
    {
        var geoObj = geoObjects.Find(obj => obj.objectId == objectId);
        if (geoObj != null)
        {
            if (geoObj.SpawnedObject != null)
            {
                OnObjectDestroyed?.Invoke(geoObj);
                DestroyImmediate(geoObj.SpawnedObject);
            }

            geoObjects.Remove(geoObj);
            spawnedObjects.Remove(geoObj);
        }
    }

    public GeoAnchoredObject GetGeoObject(string objectId)
    {
        return geoObjects.Find(obj => obj.objectId == objectId);
    }

    public void SetObjectVisibility(string objectId, bool visible)
    {
        var geoObj = GetGeoObject(objectId);
        if (geoObj != null)
        {
            geoObj.SetVisibility(visible);
        }
    }

    public void RecalculateAllDistances()
    {
        UpdateAllObjects();
    }

    private void OnDestroy()
    {
        SimpleGeoARSetup.OnLocationUpdated -= OnPlayerLocationUpdated;
        OnObjectSpawned = null;
        OnObjectDestroyed = null;
    }

    private void OnDrawGizmos()
    {
        if (!showGizmos || !Application.isPlaying) return;

        foreach (var geoObj in spawnedObjects)
        {
            if (geoObj.SpawnedObject == null) continue;

            // Draw visibility range
            Gizmos.color = geoObj.IsVisible ? Color.green : Color.red;
            Gizmos.DrawWireSphere(geoObj.SpawnedObject.transform.position, 5f);

            // Draw distance to player
            if (playerTransform != null)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawLine(playerTransform.position, geoObj.SpawnedObject.transform.position);
            }
        }
    }
}