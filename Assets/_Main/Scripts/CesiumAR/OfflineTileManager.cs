using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using CesiumForUnity;

/// <summary>
/// Manages offline tile caching and fallback systems for Cesium
/// Enables AR applications to work without internet connectivity
/// </summary>
public class OfflineTileManager : MonoBehaviour
{
    [Header("Cache Settings")]
    [SerializeField] private bool enableOfflineMode = true;
    [SerializeField] private string cacheDirectory = "CesiumCache";
    [SerializeField] private long maxCacheSizeMB = 500;
    [SerializeField] private int maxCacheAgeDays = 30;
    
    [Header("Preload Settings")]
    [SerializeField] private bool preloadAroundLocation = true;
    [SerializeField] private float preloadRadiusKm = 2f;
    [SerializeField] private int preloadZoomLevels = 3;
    [SerializeField] private bool preloadOnStart = false;
    
    [Header("Fallback Settings")]
    [SerializeField] private bool useFallbackTerrain = true;
    [SerializeField] private Material fallbackTerrainMaterial;
    [SerializeField] private Texture2D fallbackTexture;
    
    [Header("Network Settings")]
    [SerializeField] private float networkTimeoutSeconds = 10f;
    [SerializeField] private int maxRetryAttempts = 3;
    [SerializeField] private float retryDelaySeconds = 2f;
    
    [Header("Debug")]
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private bool showCacheStats = false;
    [SerializeField] private UnityEngine.UI.Text cacheStatsText;
    
    // Events
    public static event Action<float> OnPreloadProgress;
    public static event Action OnPreloadComplete;
    public static event Action<string> OnCacheError;
    public static event Action<bool> OnNetworkStatusChanged;
    
    // Properties
    public bool IsOfflineModeEnabled => enableOfflineMode;
    public bool IsNetworkAvailable { get; private set; }
    public long CurrentCacheSizeMB { get; private set; }
    public int CachedTileCount { get; private set; }
    public bool IsPreloading { get; private set; }
    
    // Private fields
    private string fullCachePath;
    private Dictionary<string, CachedTile> tileCache = new Dictionary<string, CachedTile>();
    private Coroutine preloadCoroutine;
    private Coroutine networkCheckCoroutine;
    private CesiumGeoreference cesiumReference;
    
    [System.Serializable]
    private class CachedTile
    {
        public string tileId;
        public string filePath;
        public DateTime cacheTime;
        public long fileSizeBytes;
        public int zoomLevel;
        public double latitude, longitude;
        
        public bool IsExpired(int maxAgeDays)
        {
            return (DateTime.Now - cacheTime).TotalDays > maxAgeDays;
        }
    }
    
    // Singleton pattern
    public static OfflineTileManager Instance { get; private set; }
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeCache();
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void Start()
    {
        cesiumReference = FindObjectOfType<CesiumGeoreference>();
        
        if (enableOfflineMode)
        {
            StartCoroutine(InitializeOfflineSystem());
        }
    }
    
    private void InitializeCache()
    {
        fullCachePath = Path.Combine(Application.persistentDataPath, cacheDirectory);
        
        if (!Directory.Exists(fullCachePath))
        {
            Directory.CreateDirectory(fullCachePath);
            if (enableDebugLogs) Debug.Log($"Created cache directory: {fullCachePath}");
        }
        
        LoadCacheIndex();
    }
    
    private IEnumerator InitializeOfflineSystem()
    {
        if (enableDebugLogs) Debug.Log("Initializing offline tile system...");
        
        // Start network monitoring
        networkCheckCoroutine = StartCoroutine(MonitorNetworkStatus());
        
        // Wait for GPS initialization
        yield return new WaitUntil(() => GPSCompassManager.Instance != null && GPSCompassManager.Instance.IsInitialized);
        
        // Clean expired cache
        CleanExpiredCache();
        
        // Preload tiles if enabled
        if (preloadOnStart && preloadAroundLocation)
        {
            var location = GPSCompassManager.Instance.CurrentLocation;
            StartPreloadAroundLocation(location.latitude, location.longitude);
        }
        
        if (enableDebugLogs) Debug.Log("Offline tile system initialized");
    }
    
    private IEnumerator MonitorNetworkStatus()
    {
        while (true)
        {
            bool wasAvailable = IsNetworkAvailable;
            IsNetworkAvailable = Application.internetReachability != NetworkReachability.NotReachable;
            
            if (wasAvailable != IsNetworkAvailable)
            {
                OnNetworkStatusChanged?.Invoke(IsNetworkAvailable);
                
                if (enableDebugLogs)
                {
                    Debug.Log($"Network status changed: {(IsNetworkAvailable ? "Online" : "Offline")}");
                }
            }
            
            yield return new WaitForSeconds(5f);
        }
    }
    
    private void LoadCacheIndex()
    {
        string indexPath = Path.Combine(fullCachePath, "cache_index.json");
        
        if (File.Exists(indexPath))
        {
            try
            {
                string json = File.ReadAllText(indexPath);
                var cacheData = JsonUtility.FromJson<CacheIndexData>(json);
                
                foreach (var tile in cacheData.tiles)
                {
                    if (File.Exists(tile.filePath))
                    {
                        tileCache[tile.tileId] = tile;
                    }
                }
                
                UpdateCacheStats();
                
                if (enableDebugLogs)
                {
                    Debug.Log($"Loaded cache index: {tileCache.Count} tiles, {CurrentCacheSizeMB}MB");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to load cache index: {e.Message}");
            }
        }
    }
    
    private void SaveCacheIndex()
    {
        string indexPath = Path.Combine(fullCachePath, "cache_index.json");
        
        try
        {
            var cacheData = new CacheIndexData();
            cacheData.tiles = new List<CachedTile>(tileCache.Values);
            
            string json = JsonUtility.ToJson(cacheData, true);
            File.WriteAllText(indexPath, json);
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to save cache index: {e.Message}");
        }
    }
    
    [System.Serializable]
    private class CacheIndexData
    {
        public List<CachedTile> tiles = new List<CachedTile>();
    }
    
    public void StartPreloadAroundLocation(double latitude, double longitude)
    {
        if (IsPreloading)
        {
            if (enableDebugLogs) Debug.Log("Preload already in progress");
            return;
        }
        
        if (preloadCoroutine != null)
        {
            StopCoroutine(preloadCoroutine);
        }
        
        preloadCoroutine = StartCoroutine(PreloadTilesAroundLocation(latitude, longitude));
    }
    
    private IEnumerator PreloadTilesAroundLocation(double centerLat, double centerLon)
    {
        IsPreloading = true;
        
        if (enableDebugLogs)
        {
            Debug.Log($"Starting tile preload around {centerLat:F6}, {centerLon:F6} (radius: {preloadRadiusKm}km)");
        }
        
        List<TileCoordinate> tilesToPreload = CalculateTilesToPreload(centerLat, centerLon);
        int totalTiles = tilesToPreload.Count;
        int processedTiles = 0;
        
        foreach (var tileCoord in tilesToPreload)
        {
            if (!IsNetworkAvailable && !enableOfflineMode)
            {
                if (enableDebugLogs) Debug.Log("Network unavailable, stopping preload");
                break;
            }
            
            yield return StartCoroutine(PreloadTile(tileCoord));
            
            processedTiles++;
            float progress = (float)processedTiles / totalTiles;
            OnPreloadProgress?.Invoke(progress);
            
            // Small delay to prevent overwhelming the system
            yield return new WaitForSeconds(0.1f);
        }
        
        IsPreloading = false;
        OnPreloadComplete?.Invoke();
        
        if (enableDebugLogs)
        {
            Debug.Log($"Preload complete: {processedTiles}/{totalTiles} tiles processed");
        }
    }
    
    private List<TileCoordinate> CalculateTilesToPreload(double centerLat, double centerLon)
    {
        var tiles = new List<TileCoordinate>();
        
        for (int zoom = 10; zoom <= 10 + preloadZoomLevels; zoom++)
        {
            // Calculate tile bounds for the preload radius
            double latRange = preloadRadiusKm / 111.32; // Approximate km per degree latitude
            double lonRange = preloadRadiusKm / (111.32 * Math.Cos(centerLat * Math.PI / 180));
            
            double minLat = centerLat - latRange;
            double maxLat = centerLat + latRange;
            double minLon = centerLon - lonRange;
            double maxLon = centerLon + lonRange;
            
            // Convert to tile coordinates
            var minTile = LatLonToTileCoord(minLat, minLon, zoom);
            var maxTile = LatLonToTileCoord(maxLat, maxLon, zoom);
            
            for (int x = minTile.x; x <= maxTile.x; x++)
            {
                for (int y = minTile.y; y <= maxTile.y; y++)
                {
                    tiles.Add(new TileCoordinate { x = x, y = y, zoom = zoom });
                }
            }
        }
        
        return tiles;
    }
    
    private struct TileCoordinate
    {
        public int x, y, zoom;
        
        public string ToTileId()
        {
            return $"{zoom}_{x}_{y}";
        }
    }
    
    private TileCoordinate LatLonToTileCoord(double lat, double lon, int zoom)
    {
        double n = Math.Pow(2, zoom);
        int x = (int)Math.Floor((lon + 180.0) / 360.0 * n);
        int y = (int)Math.Floor((1.0 - Math.Asinh(Math.Tan(lat * Math.PI / 180.0)) / Math.PI) / 2.0 * n);
        
        return new TileCoordinate { x = x, y = y, zoom = zoom };
    }
    
    private IEnumerator PreloadTile(TileCoordinate tileCoord)
    {
        string tileId = tileCoord.ToTileId();
        
        // Skip if already cached
        if (tileCache.ContainsKey(tileId))
        {
            yield break;
        }
        
        // Simulate tile download (in real implementation, this would download from Cesium Ion)
        yield return new WaitForSeconds(0.1f);
        
        // For demonstration, create a dummy cache entry
        var cachedTile = new CachedTile
        {
            tileId = tileId,
            filePath = Path.Combine(fullCachePath, $"{tileId}.tile"),
            cacheTime = DateTime.Now,
            fileSizeBytes = 1024, // Dummy size
            zoomLevel = tileCoord.zoom,
            latitude = 0, // Would be calculated from tile coordinates
            longitude = 0
        };
        
        tileCache[tileId] = cachedTile;
        UpdateCacheStats();
    }
    
    private void CleanExpiredCache()
    {
        var expiredTiles = new List<string>();
        
        foreach (var kvp in tileCache)
        {
            if (kvp.Value.IsExpired(maxCacheAgeDays))
            {
                expiredTiles.Add(kvp.Key);
            }
        }
        
        foreach (var tileId in expiredTiles)
        {
            RemoveCachedTile(tileId);
        }
        
        // Check cache size limit
        while (CurrentCacheSizeMB > maxCacheSizeMB && tileCache.Count > 0)
        {
            // Remove oldest tiles
            var oldestTile = GetOldestCachedTile();
            if (oldestTile != null)
            {
                RemoveCachedTile(oldestTile.tileId);
            }
        }
        
        if (expiredTiles.Count > 0 && enableDebugLogs)
        {
            Debug.Log($"Cleaned {expiredTiles.Count} expired tiles from cache");
        }
        
        SaveCacheIndex();
    }
    
    private CachedTile GetOldestCachedTile()
    {
        CachedTile oldest = null;
        DateTime oldestTime = DateTime.MaxValue;
        
        foreach (var tile in tileCache.Values)
        {
            if (tile.cacheTime < oldestTime)
            {
                oldestTime = tile.cacheTime;
                oldest = tile;
            }
        }
        
        return oldest;
    }
    
    private void RemoveCachedTile(string tileId)
    {
        if (tileCache.TryGetValue(tileId, out var tile))
        {
            try
            {
                if (File.Exists(tile.filePath))
                {
                    File.Delete(tile.filePath);
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to delete cached tile {tileId}: {e.Message}");
            }
            
            tileCache.Remove(tileId);
            UpdateCacheStats();
        }
    }
    
    private void UpdateCacheStats()
    {
        CachedTileCount = tileCache.Count;
        CurrentCacheSizeMB = 0;
        
        foreach (var tile in tileCache.Values)
        {
            CurrentCacheSizeMB += tile.fileSizeBytes;
        }
        
        CurrentCacheSizeMB /= (1024 * 1024); // Convert to MB
        
        UpdateCacheStatsUI();
    }
    
    private void UpdateCacheStatsUI()
    {
        if (!showCacheStats || cacheStatsText == null) return;
        
        string stats = $"Cache: {CachedTileCount} tiles\n";
        stats += $"Size: {CurrentCacheSizeMB}MB / {maxCacheSizeMB}MB\n";
        stats += $"Network: {(IsNetworkAvailable ? "Online" : "Offline")}\n";
        stats += $"Preloading: {(IsPreloading ? "Yes" : "No")}";
        
        cacheStatsText.text = stats;
    }
    
    // Public methods
    public void ClearCache()
    {
        try
        {
            if (Directory.Exists(fullCachePath))
            {
                Directory.Delete(fullCachePath, true);
                Directory.CreateDirectory(fullCachePath);
            }
            
            tileCache.Clear();
            UpdateCacheStats();
            
            if (enableDebugLogs) Debug.Log("Cache cleared successfully");
        }
        catch (Exception e)
        {
            OnCacheError?.Invoke($"Failed to clear cache: {e.Message}");
        }
    }
    
    public void StopPreload()
    {
        if (preloadCoroutine != null)
        {
            StopCoroutine(preloadCoroutine);
            preloadCoroutine = null;
        }
        
        IsPreloading = false;
    }
    
    private void OnDestroy()
    {
        StopPreload();
        
        if (networkCheckCoroutine != null)
        {
            StopCoroutine(networkCheckCoroutine);
        }
        
        SaveCacheIndex();
        
        // Clear events
        OnPreloadProgress = null;
        OnPreloadComplete = null;
        OnCacheError = null;
        OnNetworkStatusChanged = null;
    }
    
    private void Update()
    {
        if (showCacheStats && Time.frameCount % 60 == 0) // Update UI every second at 60fps
        {
            UpdateCacheStatsUI();
        }
    }
}
