# Geo-AR System Documentation

## Tổng quan

Hệ thống Geo-AR này cho phép bạn tạo ứng dụng AR trên Android sử dụng Unity, AR Foundation và Cesium for Unity, trong đó các mô hình 3D được neo chính xác vào vị trí địa lý thực tế mà không cần dịch vụ định vị trả phí.

## Các tính năng chính

- ✅ GPS và Compass tracking chính xác
- ✅ Tự động căn chỉnh Cesium origin với vị trí thiết bị
- ✅ Spawn multiple objects tại tọa độ địa lý cụ thể
- ✅ Distance-based culling và LOD system
- ✅ Offline tile caching (cơ bản)
- ✅ Mobile performance optimization
- ✅ Battery optimization
- ✅ Auto-calibration system
- ✅ Error handling và recovery

## Cấu trúc Scripts

### Core Scripts

1. **GeoARManager.cs** - Main manager đi<PERSON><PERSON> phối toàn bộ hệ thống
2. **GPSCompassManager.cs** - Quản lý GPS và compass với optimization
3. **SpawnAnchoredObject.cs** - Spawn và quản lý objects tại vị trí địa lý
4. **SimpleGeoARSetup.cs** - Setup cơ bản (đã được nâng cấp)

### Optimization Scripts

5. **MobilePerformanceOptimizer.cs** - Tối ưu hiệu năng cho mobile
6. **OfflineTileManager.cs** - Quản lý cache tiles offline

### Demo Scripts

7. **GeoARDemo.cs** - Demo script minh họa cách sử dụng

## Setup Instructions

### 1. Unity Project Setup

```
Unity Version: 2022.3 LTS hoặc mới hơn
Platform: Android
```

### 2. Required Packages

Packages đã có trong project:
- AR Foundation (6.1.1)
- ARCore XR Plugin (6.1.1)
- XR Interaction Toolkit (3.1.1)
- Cesium for Unity (1.17.0)
- Universal Render Pipeline (17.0.3)

### 3. Scene Setup

#### A. Tạo Scene mới hoặc sử dụng scene hiện có

#### B. Setup AR Foundation
1. Tạo GameObject với ARSession component
2. Tạo XR Origin (AR Rig) với:
   - AR Camera
   - AR Session Origin
   - AR Plane Manager (optional)
   - AR Raycast Manager (optional)

#### C. Setup Cesium
1. Tạo GameObject với CesiumGeoreference component
2. Thêm Cesium3DTileset nếu cần (cho terrain/buildings)

#### D. Add Core Scripts
1. Tạo GameObject "GeoARManager" và add script GeoARManager.cs
2. Assign CesiumGeoreference vào GeoARManager
3. Tạo GameObject "ObjectSpawner" và add script SpawnAnchoredObject.cs
4. Assign CesiumGeoreference vào SpawnAnchoredObject

### 4. Android Build Settings

#### A. Player Settings
```
Company Name: [Your Company]
Product Name: [Your App Name]
Package Name: com.yourcompany.yourapp
Minimum API Level: Android 7.0 (API level 24)
Target API Level: Automatic (highest installed)
Scripting Backend: IL2CPP
Target Architectures: ARM64
```

#### B. XR Settings
```
Initialize XR on Startup: Checked
XR Providers: ARCore
```

#### C. Required Permissions
Thêm vào AndroidManifest.xml:
```xml
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### 5. Script Configuration

#### A. GeoARManager Setup
```csharp
// Assign trong Inspector:
- Cesium Reference: CesiumGeoreference GameObject
- AR Session: ARSession GameObject  
- AR Camera: Main Camera
- Loading UI: UI Panel cho loading
- Status Text: Text component cho status
```

#### B. SpawnAnchoredObject Setup
```csharp
// Trong Inspector:
- Cesium Reference: CesiumGeoreference GameObject
- Geo Objects: List các object muốn spawn
  - Object ID: Unique identifier
  - Prefab: 3D model prefab
  - Longitude/Latitude/Altitude: Tọa độ địa lý
  - Max Visibility Distance: Khoảng cách tối đa hiển thị
```

#### C. Demo Setup (Optional)
```csharp
// Add GeoARDemo.cs vào GameObject
// Setup UI elements:
- Spawn Object Button
- Clear Objects Button  
- Recalibrate Button
- Location Dropdown
- Info Text
```

## Cách sử dụng

### 1. Basic Usage

```csharp
// Khởi tạo hệ thống (tự động nếu autoInitialize = true)
GeoARManager.Instance.RestartSystem();

// Spawn object tại tọa độ cụ thể
var geoObject = new GeoAnchoredObject
{
    objectId = "my_object_1",
    prefab = myPrefab,
    longitude = 105.8522, // Hồ Hoàn Kiếm
    latitude = 21.0285,
    altitude = 10,
    maxVisibilityDistance = 1000f
};

SpawnAnchoredObject.Instance.AddGeoObject(geoObject);
```

### 2. Event Handling

```csharp
// Subscribe to events
GeoARManager.OnSystemInitialized += OnSystemReady;
GeoARManager.OnSystemError += OnSystemError;
GPSCompassManager.OnLocationUpdated += OnLocationChanged;
SpawnAnchoredObject.OnObjectSpawned += OnObjectSpawned;

void OnSystemReady()
{
    Debug.Log("Geo-AR system is ready!");
    // Spawn your objects here
}
```

### 3. Manual Calibration

```csharp
// Recalibrate manually
GeoARManager.Instance.ManualRecalibrate();

// Check if system is calibrated
if (GeoARManager.Instance.IsCalibrated)
{
    // System is ready for accurate positioning
}
```

## Performance Optimization

### 1. Distance Culling
```csharp
// Trong SpawnAnchoredObject
enableDistanceCulling = true;
maxVisibleObjects = 50; // Giới hạn số object hiển thị
```

### 2. LOD System
```csharp
// Setup LOD cho object
geoObject.useLOD = true;
geoObject.lodPrefabs = new GameObject[] { highDetailPrefab, lowDetailPrefab };
geoObject.lodDistances = new float[] { 100f, 500f };
```

### 3. Battery Optimization
```csharp
// MobilePerformanceOptimizer tự động điều chỉnh
// Có thể force performance level:
MobilePerformanceOptimizer.Instance.ForcePerformanceLevel(PerformanceLevel.Low);
```

## Troubleshooting

### 1. GPS không hoạt động
- Kiểm tra permissions trong AndroidManifest.xml
- Test trên thiết bị thật (không phải emulator)
- Đảm bảo Location Services được bật

### 2. AR tracking không ổn định
- Kiểm tra lighting conditions
- Đảm bảo có đủ visual features trong môi trường
- Kiểm tra ARCore compatibility

### 3. Objects không hiển thị
- Kiểm tra distance culling settings
- Verify tọa độ longitude/latitude
- Check object scale và position

### 4. Performance issues
- Giảm maxVisibleObjects
- Enable LOD system
- Reduce render scale trong MobilePerformanceOptimizer

## Offline Mode

### 1. Enable Offline Tiles
```csharp
// OfflineTileManager tự động cache tiles
// Preload tiles around location:
OfflineTileManager.Instance.StartPreloadAroundLocation(latitude, longitude);
```

### 2. Cache Management
```csharp
// Clear cache
OfflineTileManager.Instance.ClearCache();

// Check cache size
long cacheSizeMB = OfflineTileManager.Instance.CurrentCacheSizeMB;
```

## Advanced Features

### 1. Custom Object Behaviors
```csharp
// Extend GeoAnchoredObject cho custom behaviors
public class InteractiveGeoObject : GeoAnchoredObject
{
    public UnityEvent OnPlayerNear;
    public float interactionDistance = 5f;
    
    // Custom update logic
}
```

### 2. Real-time Object Updates
```csharp
// Update object position at runtime
var geoObject = SpawnAnchoredObject.Instance.GetGeoObject("my_object");
if (geoObject != null)
{
    geoObject.longitude = newLongitude;
    geoObject.latitude = newLatitude;
    // Object sẽ tự động update position
}
```

## Best Practices

1. **Testing**: Luôn test trên thiết bị thật với GPS
2. **Permissions**: Request permissions trước khi khởi tạo
3. **Error Handling**: Implement proper error handling cho GPS/AR failures
4. **Performance**: Monitor frame rate và memory usage
5. **Battery**: Implement battery-aware features
6. **Calibration**: Provide manual recalibration option cho users
7. **Offline**: Preload tiles cho areas quan trọng

## Known Limitations

1. Độ chính xác GPS phụ thuộc vào thiết bị và môi trường
2. Compass có thể bị ảnh hưởng bởi từ trường
3. AR tracking cần lighting và visual features tốt
4. Performance phụ thuộc vào hardware thiết bị
5. Offline tiles chỉ là demo implementation

## Future Improvements

1. Integration với real Cesium Ion API
2. Advanced offline tile management
3. Multi-user synchronization
4. Cloud anchor support
5. Advanced occlusion handling
6. Machine learning-based calibration
