using UnityEngine;
using CesiumForUnity;

/// <summary>
/// Simple script to test geo-object spawning without complex alignment
/// Use this to debug the coordinate system issues
/// </summary>
public class SimpleGeoObjectSpawner : MonoBehaviour
{
    [Header("Test Settings")]
    public CesiumGeoreference cesiumReference;
    public GameObject testPrefab;
    
    [Header("Test Locations")]
    public TestLocation[] testLocations = new TestLocation[]
    {
        new TestLocation { name = "North 100m", longitude = 0, latitude = 0.0009, altitude = 10 },
        new TestLocation { name = "East 100m", longitude = 0.0009, latitude = 0, altitude = 10 },
        new TestLocation { name = "South 100m", longitude = 0, latitude = -0.0009, altitude = 10 },
        new TestLocation { name = "West 100m", longitude = -0.0009, latitude = 0, altitude = 10 }
    };
    
    [Header("Debug")]
    public bool enableDebugLogs = true;
    public bool showGizmos = true;
    
    [System.Serializable]
    public class TestLocation
    {
        public string name;
        public double longitude;
        public double latitude;
        public double altitude;
        [HideInInspector] public GameObject spawnedObject;
    }
    
    private GPSCompassManager gpsManager;
    private bool isInitialized = false;
    
    private void Start()
    {
        // Wait for GPS manager
        gpsManager = GPSCompassManager.Instance;
        if (gpsManager != null)
        {
            GPSCompassManager.OnLocationUpdated += OnLocationUpdated;
        }
        
        // Set Cesium origin to (0,0,0) for testing
        if (cesiumReference != null)
        {
            cesiumReference.SetOriginLongitudeLatitudeHeight(0, 0, 0);
            cesiumReference.transform.rotation = Quaternion.identity;
        }
    }
    
    private void OnLocationUpdated(LocationInfo location)
    {
        if (!isInitialized && gpsManager.IsGPSRunning)
        {
            InitializeWithCurrentLocation(location);
        }
    }
    
    private void InitializeWithCurrentLocation(LocationInfo location)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"Initializing with GPS location: {location.latitude:F6}, {location.longitude:F6}");
        }
        
        // Set Cesium origin to current GPS location
        cesiumReference.SetOriginLongitudeLatitudeHeight(
            location.longitude,
            location.latitude,
            location.altitude > 0 ? location.altitude : 0
        );
        
        // Update test locations to be relative to current position
        for (int i = 0; i < testLocations.Length; i++)
        {
            testLocations[i].longitude += location.longitude;
            testLocations[i].latitude += location.latitude;
            testLocations[i].altitude += location.altitude > 0 ? location.altitude : 0;
        }
        
        isInitialized = true;
        
        if (enableDebugLogs)
        {
            Debug.Log("SimpleGeoObjectSpawner initialized. Ready to spawn test objects.");
        }
    }
    
    [ContextMenu("Spawn Test Objects")]
    public void SpawnTestObjects()
    {
        if (!isInitialized)
        {
            Debug.LogWarning("Not initialized yet. Wait for GPS.");
            return;
        }
        
        if (testPrefab == null)
        {
            Debug.LogError("Test prefab not assigned!");
            return;
        }
        
        foreach (var testLoc in testLocations)
        {
            SpawnObjectAtLocation(testLoc);
        }
    }
    
    [ContextMenu("Clear Test Objects")]
    public void ClearTestObjects()
    {
        foreach (var testLoc in testLocations)
        {
            if (testLoc.spawnedObject != null)
            {
                DestroyImmediate(testLoc.spawnedObject);
                testLoc.spawnedObject = null;
            }
        }
    }
    
    private void SpawnObjectAtLocation(TestLocation testLoc)
    {
        if (testLoc.spawnedObject != null)
        {
            DestroyImmediate(testLoc.spawnedObject);
        }
        
        // Spawn object
        GameObject obj = Instantiate(testPrefab, cesiumReference.transform);
        obj.name = $"TestObject_{testLoc.name}";
        
        // Add CesiumGlobeAnchor
        var anchor = obj.GetComponent<CesiumGlobeAnchor>();
        if (anchor == null)
        {
            anchor = obj.AddComponent<CesiumGlobeAnchor>();
        }
        
        // Set position
        anchor.SetPositionLongitudeLatitudeHeight(
            testLoc.longitude,
            testLoc.latitude,
            testLoc.altitude
        );
        
        anchor.adjustOrientationForGlobeWhenMoving = false;
        anchor.detectTransformChanges = false;
        
        testLoc.spawnedObject = obj;
        
        if (enableDebugLogs)
        {
            Debug.Log($"Spawned {testLoc.name} at {testLoc.longitude:F6}, {testLoc.latitude:F6}, {testLoc.altitude:F1}");
            Debug.Log($"Unity position: {obj.transform.position}");
        }
    }
    
    [ContextMenu("Spawn Single North Object")]
    public void SpawnSingleNorthObject()
    {
        if (!isInitialized || testPrefab == null) return;
        
        var currentLocation = gpsManager.CurrentLocation;
        
        // Spawn object 50 meters north
        double northLat = currentLocation.latitude + (50.0 / 111320.0); // ~50 meters north
        
        var testLoc = new TestLocation
        {
            name = "North50m",
            longitude = currentLocation.longitude,
            latitude = northLat,
            altitude = currentLocation.altitude + 5
        };
        
        SpawnObjectAtLocation(testLoc);
    }
    
    [ContextMenu("Debug Current Position")]
    public void DebugCurrentPosition()
    {
        if (gpsManager == null || !gpsManager.IsGPSRunning)
        {
            Debug.Log("GPS not available");
            return;
        }
        
        var location = gpsManager.CurrentLocation;
        var heading = gpsManager.CurrentHeading;
        
        Debug.Log($"=== CURRENT POSITION DEBUG ===");
        Debug.Log($"GPS: {location.latitude:F8}, {location.longitude:F8}, {location.altitude:F2}m");
        Debug.Log($"Heading: {heading:F1}°");
        Debug.Log($"Accuracy: {location.horizontalAccuracy:F1}m");
        
        if (cesiumReference != null)
        {
            Debug.Log($"Cesium Origin: {cesiumReference.longitude:F8}, {cesiumReference.latitude:F8}, {cesiumReference.height:F2}");
            Debug.Log($"Cesium Rotation: {cesiumReference.transform.rotation.eulerAngles}");
        }
        
        // Camera info
        var camera = Camera.main;
        if (camera != null)
        {
            Debug.Log($"Camera Position: {camera.transform.position}");
            Debug.Log($"Camera Rotation: {camera.transform.rotation.eulerAngles}");
        }
    }
    
    /*[ContextMenu("Test Coordinate Conversion")]
    public void TestCoordinateConversion()
    {
        if (!gpsManager.IsGPSRunning) return;
        
        var currentLocation = gpsManager.CurrentLocation;
        
        /#1#/ Test points at known distances
        var testPoints = new[]
        {
            new { name = "Origin", lat = currentLocation.latitude, lon = currentLocation.longitude },
            new { name = "100m North", lat = currentLocation.latitude + (100.0 / 111320.0), lon = currentLocation.longitude },
            new { name = "100m East", lat = currentLocation.latitude, lon = currentLocation.longitude + (100.0 / (111320.0 * System.Math.Cos(currentLocation.latitude * System.Math.PI / 180.0))) },
        };#1#
        
        Debug.Log("=== COORDINATE CONVERSION TEST ===");
        
        foreach (var point in testPoints)
        {
            // Create temporary object to test conversion
            var tempObj = new GameObject("TempTest");
            tempObj.transform.SetParent(cesiumReference.transform);
            
            var anchor = tempObj.AddComponent<CesiumGlobeAnchor>();
            anchor.SetPositionLongitudeLatitudeHeight(point.lon, point.lat, currentLocation.altitude);
            
            Debug.Log($"{point.name}: GPS({point.lat:F8}, {point.lon:F8}) -> Unity({tempObj.transform.position})");
            
            DestroyImmediate(tempObj);
        }
    }*/
    
    private void OnDrawGizmos()
    {
        if (!showGizmos || !isInitialized) return;
        
        // Draw test object positions
        foreach (var testLoc in testLocations)
        {
            if (testLoc.spawnedObject != null)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(testLoc.spawnedObject.transform.position, 2f);
                
                // Draw line from origin
                Gizmos.color = Color.green;
                Gizmos.DrawLine(Vector3.zero, testLoc.spawnedObject.transform.position);
            }
        }
        
        // Draw coordinate axes at origin
        Gizmos.color = Color.red;
        Gizmos.DrawLine(Vector3.zero, Vector3.right * 20f); // East
        
        Gizmos.color = Color.blue;
        Gizmos.DrawLine(Vector3.zero, Vector3.forward * 20f); // North
        
        Gizmos.color = Color.green;
        Gizmos.DrawLine(Vector3.zero, Vector3.up * 20f); // Up
    }
    
    private void OnDestroy()
    {
        if (gpsManager != null)
        {
            GPSCompassManager.OnLocationUpdated -= OnLocationUpdated;
        }
    }
    
    // UI Button methods
    public void OnSpawnTestObjectsButton()
    {
        SpawnTestObjects();
    }
    
    public void OnClearTestObjectsButton()
    {
        ClearTestObjects();
    }
    
    public void OnDebugPositionButton()
    {
        DebugCurrentPosition();
    }
    
    /*public void OnTestCoordinateButton()
    {
        TestCoordinateConversion();
    }*/
}
