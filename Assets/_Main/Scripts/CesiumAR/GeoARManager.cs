using System;
using System.Collections;
using UnityEngine;
using UnityEngine.XR.ARFoundation;
using Unity.XR.CoreUtils;
using CesiumForUnity;
using TMPro;

/// <summary>
/// Main manager for the Geo-AR system
/// Coordinates GPS, Compass, Cesium, and AR components
/// </summary>
public class GeoARManager : MonoBehaviour
{
    [Header("Core Components")]
    [SerializeField] private CesiumGeoreference cesiumReference;
    [SerializeField] private ARSession arSession;
    [SerializeField] private XROrigin xrOrigin;
    [SerializeField] private Camera arCamera;

    [Header("System Settings")]
    [SerializeField] private bool autoInitialize = true;
    [SerializeField] private bool enableOfflineMode = true;
    [SerializeField] private bool enablePerformanceOptimization = true;
    [SerializeField] private float initializationTimeout = 30f;

    [Header("Calibration Settings")]
    [SerializeField] private bool enableAutoCalibration = true;
    [SerializeField] private float calibrationInterval = 60f;
    [SerializeField] private float maxPositionDrift = 10f; // meters
    [SerializeField] private float maxHeadingDrift = 15f; // degrees

    [Header("UI References")]
    [SerializeField] private GameObject loadingUI;
    [SerializeField] private GameObject errorUI;
    [SerializeField] private TextMeshProUGUI statusText;
    [SerializeField] private UnityEngine.UI.Button recalibrateButton;

    [Header("Debug")]
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private bool showSystemStatus = false;

    // System state
    public enum SystemState
    {
        Uninitialized,
        Initializing,
        WaitingForGPS,
        WaitingForAR,
        Calibrating,
        Running,
        Error,
        Paused
    }

    private SystemState currentState = SystemState.Uninitialized;
    private SystemState previousState = SystemState.Uninitialized;

    // Components
    private GPSCompassManager gpsManager;
    private SpawnAnchoredObject objectSpawner;
    private OfflineTileManager tileManager;
    private MobilePerformanceOptimizer performanceOptimizer;
    private CesiumARAligner cesiumAligner;

    // Calibration data
    private Vector3 initialARPosition;
    private Quaternion initialARRotation;
    private LocationInfo initialGPSLocation;
    private float initialHeading;
    private bool isCalibrated = false;

    // Tracking
    private Coroutine calibrationCoroutine;
    private float lastCalibrationTime;

    // Events
    public static event Action<SystemState> OnSystemStateChanged;
    public static event Action OnSystemInitialized;
    public static event Action<string> OnSystemError;
    public static event Action OnCalibrationComplete;

    // Properties
    public SystemState CurrentState => currentState;
    public bool IsInitialized => currentState == SystemState.Running;
    public bool IsCalibrated => isCalibrated;
    public CesiumGeoreference CesiumReference => cesiumReference;

    // Singleton
    public static GeoARManager Instance { get; private set; }

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        if (autoInitialize)
        {
            StartCoroutine(InitializeSystem());
        }
    }

    private IEnumerator InitializeSystem()
    {
        SetSystemState(SystemState.Initializing);

        if (enableDebugLogs) Debug.Log("Starting Geo-AR system initialization...");

        // Show loading UI
        if (loadingUI != null) loadingUI.SetActive(true);
        if (errorUI != null) errorUI.SetActive(false);

        // Validate components
        if (!ValidateComponents())
        {
            SetSystemError("Missing required components");
            yield break;
        }

        // Initialize core systems
        yield return StartCoroutine(InitializeCoreComponents());

        // Wait for GPS
        SetSystemState(SystemState.WaitingForGPS);
        yield return StartCoroutine(WaitForGPSInitialization());

        // Wait for AR
        SetSystemState(SystemState.WaitingForAR);
        yield return StartCoroutine(WaitForARInitialization());

        // Perform initial calibration
        SetSystemState(SystemState.Calibrating);
        yield return StartCoroutine(PerformInitialCalibration());

        // Start continuous calibration if enabled
        if (enableAutoCalibration)
        {
            calibrationCoroutine = StartCoroutine(ContinuousCalibration());
        }

        // System ready
        SetSystemState(SystemState.Running);
        OnSystemInitialized?.Invoke();

        // Hide loading UI
        if (loadingUI != null) loadingUI.SetActive(false);

        if (enableDebugLogs) Debug.Log("Geo-AR system initialization complete!");
    }

    private bool ValidateComponents()
    {
        if (cesiumReference == null)
        {
            Debug.LogError("CesiumGeoreference not assigned!");
            return false;
        }

        if (arSession == null)
        {
            arSession = FindObjectOfType<ARSession>();
            if (arSession == null)
            {
                Debug.LogError("ARSession not found!");
                return false;
            }
        }

        if (arCamera == null)
        {
            arCamera = Camera.main;
            if (arCamera == null)
            {
                Debug.LogError("AR Camera not found!");
                return false;
            }
        }

        return true;
    }

    private IEnumerator InitializeCoreComponents()
    {
        // Initialize GPS/Compass Manager
        gpsManager = GPSCompassManager.Instance;
        if (gpsManager == null)
        {
            var gpsGO = new GameObject("GPSCompassManager");
            gpsManager = gpsGO.AddComponent<GPSCompassManager>();
        }

        // Initialize Object Spawner
        objectSpawner = FindObjectOfType<SpawnAnchoredObject>();

        // Initialize Offline Tile Manager
        if (enableOfflineMode)
        {
            tileManager = OfflineTileManager.Instance;
            if (tileManager == null)
            {
                var tileGO = new GameObject("OfflineTileManager");
                tileManager = tileGO.AddComponent<OfflineTileManager>();
            }
        }

        // Initialize Performance Optimizer
        if (enablePerformanceOptimization)
        {
            performanceOptimizer = MobilePerformanceOptimizer.Instance;
            if (performanceOptimizer == null)
            {
                var perfGO = new GameObject("MobilePerformanceOptimizer");
                performanceOptimizer = perfGO.AddComponent<MobilePerformanceOptimizer>();
            }
        }

        // Initialize Cesium AR Aligner
        cesiumAligner = FindObjectOfType<CesiumARAligner>();
        if (cesiumAligner == null)
        {
            var alignerGO = new GameObject("CesiumARAligner");
            cesiumAligner = alignerGO.AddComponent<CesiumARAligner>();

            // Configure aligner with our references
            cesiumAligner.cesiumReference = cesiumReference;
            cesiumAligner.arCameraTransform = arCamera.transform;
            cesiumAligner.xrOrigin = xrOrigin;
        }

        yield return new WaitForSeconds(0.5f); // Allow components to initialize
    }

    private IEnumerator WaitForGPSInitialization()
    {
        float timeout = initializationTimeout;

        while (!gpsManager.IsInitialized && timeout > 0)
        {
            UpdateStatusText("Waiting for GPS...");
            yield return new WaitForSeconds(0.5f);
            timeout -= 0.5f;
        }

        if (!gpsManager.IsInitialized)
        {
            SetSystemError("GPS initialization timeout");
            yield break;
        }

        if (enableDebugLogs) Debug.Log("GPS initialized successfully");
    }

    private IEnumerator WaitForARInitialization()
    {
        float timeout = initializationTimeout;

        while (ARSession.state != ARSessionState.SessionTracking && timeout > 0)
        {
            UpdateStatusText($"Waiting for AR... ({ARSession.state})");
            yield return new WaitForSeconds(0.5f);
            timeout -= 0.5f;
        }

        if (ARSession.state != ARSessionState.SessionTracking)
        {
            SetSystemError("AR initialization timeout");
            yield break;
        }

        if (enableDebugLogs) Debug.Log("AR initialized successfully");
    }

    private IEnumerator PerformInitialCalibration()
    {
        UpdateStatusText("Calibrating system...");

        // Wait for stable GPS and AR tracking
        yield return new WaitForSeconds(2f);

        // Store initial AR state
        initialARPosition = arCamera.transform.position;
        initialARRotation = arCamera.transform.rotation;

        // Store initial GPS state
        initialGPSLocation = gpsManager.CurrentLocation;
        initialHeading = gpsManager.CurrentHeading;

        // Use CesiumARAligner for proper alignment
        if (cesiumAligner != null)
        {
            // Wait for aligner to complete
            yield return new WaitUntil(() => cesiumAligner.IsAligned);

            if (enableDebugLogs)
            {
                Debug.Log("CesiumARAligner completed alignment");
            }
        }
        else
        {
            // Fallback: simple Cesium origin setting
            cesiumReference.SetOriginLongitudeLatitudeHeight(
                initialGPSLocation.longitude,
                initialGPSLocation.latitude,
                initialGPSLocation.altitude > 0 ? initialGPSLocation.altitude : 0
            );
        }

        isCalibrated = true;
        lastCalibrationTime = Time.time;

        OnCalibrationComplete?.Invoke();

        if (enableDebugLogs)
        {
            Debug.Log($"Initial calibration complete. Origin: {initialGPSLocation.latitude:F6}, {initialGPSLocation.longitude:F6}, Heading: {initialHeading:F1}°");
        }
    }

    private IEnumerator ContinuousCalibration()
    {
        while (currentState == SystemState.Running)
        {
            yield return new WaitForSeconds(calibrationInterval);

            if (ShouldRecalibrate())
            {
                yield return StartCoroutine(PerformRecalibration());
            }
        }
    }

    private bool ShouldRecalibrate()
    {
        if (!isCalibrated || !gpsManager.IsInitialized) return false;

        // Check position drift
        var currentLocation = gpsManager.CurrentLocation;
        float positionDrift = CalculateDistance(
            initialGPSLocation.latitude, initialGPSLocation.longitude,
            currentLocation.latitude, currentLocation.longitude
        );

        // Check heading drift
        float headingDrift = Mathf.Abs(Mathf.DeltaAngle(initialHeading, gpsManager.CurrentHeading));

        bool shouldRecalibrate = positionDrift > maxPositionDrift || headingDrift > maxHeadingDrift;

        if (shouldRecalibrate && enableDebugLogs)
        {
            Debug.Log($"Recalibration needed. Position drift: {positionDrift:F1}m, Heading drift: {headingDrift:F1}°");
        }

        return shouldRecalibrate;
    }

    private IEnumerator PerformRecalibration()
    {
        if (enableDebugLogs) Debug.Log("Performing system recalibration...");

        UpdateStatusText("Recalibrating...");

        // Update Cesium origin
        var currentLocation = gpsManager.CurrentLocation;
        cesiumReference.SetOriginLongitudeLatitudeHeight(
            currentLocation.longitude,
            currentLocation.latitude,
            currentLocation.altitude > 0 ? currentLocation.altitude : 0
        );

        // Update AR Camera alignment instead of rotating Cesium world
        float currentHeading = gpsManager.CurrentHeading;
        initialHeading = currentHeading;
        AlignARCameraWithNorth();

        // Update stored values
        initialGPSLocation = currentLocation;
        lastCalibrationTime = Time.time;

        yield return new WaitForSeconds(1f);

        if (enableDebugLogs) Debug.Log("Recalibration complete");
    }

    private void AlignARCameraWithNorth()
    {
        if (arCamera == null || !gpsManager.IsCompassEnabled) return;

        // Tính toán offset rotation để align với true north
        float headingOffset = initialHeading;

        // Tạo một parent object để handle rotation offset
        GameObject northAlignmentParent = GameObject.Find("NorthAlignmentParent");
        if (northAlignmentParent == null)
        {
            northAlignmentParent = new GameObject("NorthAlignmentParent");
            northAlignmentParent.transform.position = Vector3.zero;
        }

        // Set rotation để compensate cho heading difference
        northAlignmentParent.transform.rotation = Quaternion.Euler(0f, headingOffset, 0f);

        // Parent AR camera hierarchy vào alignment parent
        if (xrOrigin != null)
        {
            xrOrigin.transform.SetParent(northAlignmentParent.transform, true);
        }
        else if (arCamera.transform.parent != null)
        {
            arCamera.transform.parent.SetParent(northAlignmentParent.transform, true);
        }

        if (enableDebugLogs)
        {
            Debug.Log($"AR Camera aligned with north. Heading offset: {headingOffset:F1}°");
        }
    }

    private void UpdateHeadingAlignment()
    {
        if (!isCalibrated || !gpsManager.IsCompassEnabled) return;

        GameObject northAlignmentParent = GameObject.Find("NorthAlignmentParent");
        if (northAlignmentParent != null)
        {
            float currentHeading = gpsManager.CurrentHeading;
            float headingDrift = Mathf.DeltaAngle(initialHeading, currentHeading);

            // Chỉ update nếu drift đáng kể
            if (Mathf.Abs(headingDrift) > 5f)
            {
                northAlignmentParent.transform.rotation = Quaternion.Euler(0f, currentHeading, 0f);

                if (enableDebugLogs)
                {
                    Debug.Log($"Updated heading alignment. Drift: {headingDrift:F1}°");
                }
            }
        }
    }

    private float CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371000; // Earth radius in meters
        double dLat = (lat2 - lat1) * Mathf.Deg2Rad;
        double dLon = (lon2 - lon1) * Mathf.Deg2Rad;

        double a = Mathf.Sin((float)dLat / 2) * Mathf.Sin((float)dLat / 2) +
                   Mathf.Cos((float)(lat1 * Mathf.Deg2Rad)) * Mathf.Cos((float)(lat2 * Mathf.Deg2Rad)) *
                   Mathf.Sin((float)dLon / 2) * Mathf.Sin((float)dLon / 2);

        double c = 2 * Mathf.Atan2(Mathf.Sqrt((float)a), Mathf.Sqrt((float)(1 - a)));
        return (float)(R * c);
    }

    private void SetSystemState(SystemState newState)
    {
        if (newState != currentState)
        {
            previousState = currentState;
            currentState = newState;

            OnSystemStateChanged?.Invoke(currentState);

            if (enableDebugLogs)
            {
                Debug.Log($"System state changed: {previousState} -> {currentState}");
            }

            UpdateStatusText(GetStateDescription(currentState));
        }
    }

    private void SetSystemError(string errorMessage)
    {
        SetSystemState(SystemState.Error);
        OnSystemError?.Invoke(errorMessage);

        if (errorUI != null) errorUI.SetActive(true);
        if (loadingUI != null) loadingUI.SetActive(false);

        Debug.LogError($"Geo-AR System Error: {errorMessage}");
    }

    private string GetStateDescription(SystemState state)
    {
        switch (state)
        {
            case SystemState.Uninitialized: return "System not initialized";
            case SystemState.Initializing: return "Initializing system...";
            case SystemState.WaitingForGPS: return "Waiting for GPS...";
            case SystemState.WaitingForAR: return "Waiting for AR...";
            case SystemState.Calibrating: return "Calibrating...";
            case SystemState.Running: return "System ready";
            case SystemState.Error: return "System error";
            case SystemState.Paused: return "System paused";
            default: return "Unknown state";
        }
    }

    private void UpdateStatusText(string message)
    {
        if (statusText != null)
        {
            statusText.text = message;
        }
    }

    // Public methods
    public void ManualRecalibrate()
    {
        if (currentState == SystemState.Running)
        {
            if (cesiumAligner != null)
            {
                cesiumAligner.ForceRealignment();
            }
            else
            {
                StartCoroutine(PerformRecalibration());
            }
        }
    }

    public void PauseSystem()
    {
        if (currentState == SystemState.Running)
        {
            SetSystemState(SystemState.Paused);

            if (calibrationCoroutine != null)
            {
                StopCoroutine(calibrationCoroutine);
                calibrationCoroutine = null;
            }
        }
    }

    public void ResumeSystem()
    {
        if (currentState == SystemState.Paused)
        {
            SetSystemState(SystemState.Running);

            if (enableAutoCalibration)
            {
                calibrationCoroutine = StartCoroutine(ContinuousCalibration());
            }
        }
    }

    public void RestartSystem()
    {
        StopAllCoroutines();
        SetSystemState(SystemState.Uninitialized);
        StartCoroutine(InitializeSystem());
    }

    private void OnEnable()
    {
        if (recalibrateButton != null)
        {
            recalibrateButton.onClick.AddListener(ManualRecalibrate);
        }
    }

    private void OnDisable()
    {
        if (recalibrateButton != null)
        {
            recalibrateButton.onClick.RemoveListener(ManualRecalibrate);
        }
    }

    private void OnDestroy()
    {
        // Clear events
        OnSystemStateChanged = null;
        OnSystemInitialized = null;
        OnSystemError = null;
        OnCalibrationComplete = null;
    }

    private void Update()
    {
        // Update heading alignment continuously
        if (currentState == SystemState.Running && Time.frameCount % 30 == 0)
        {
            UpdateHeadingAlignment();
        }

        if (showSystemStatus && statusText != null && Time.frameCount % 30 == 0)
        {
            string status = $"State: {currentState}\n";
            if (gpsManager != null)
            {
                status += $"GPS: {(gpsManager.IsGPSRunning ? "ON" : "OFF")}\n";
                status += $"Compass: {(gpsManager.IsCompassEnabled ? "ON" : "OFF")}\n";
            }
            status += $"AR: {ARSession.state}\n";
            status += $"Calibrated: {(isCalibrated ? "YES" : "NO")}";

            UpdateStatusText(status);
        }
    }
}
