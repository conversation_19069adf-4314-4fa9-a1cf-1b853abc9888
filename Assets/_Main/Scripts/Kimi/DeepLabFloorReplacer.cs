using UnityEngine;
using Unity.Barracuda;
using System.Collections;

public class DeepLabFloorSegment520 : MonoBehaviour
{
    [Header("Assets")]
    public NNModel modelAsset;
    public Texture2D floorTexture;
    public Material floorMat;                    // shader b<PERSON><PERSON> <PERSON>

    [Header("Runtime")]
    public float floorY = 0f;
    const int INPUT_SIZE = 520;                  // khớp model

    WebCamTexture camTex;
    RenderTexture inputRT;                       // 520×520 RGB
    RenderTexture outputRT;                      // 520×520 R8 (class index)
    IWorker worker;
    Camera cam;

    void Start()
    {
        cam = GetComponent<Camera>();
        cam.depthTextureMode |= DepthTextureMode.Depth;

        // Camera 520×520
        camTex = new WebCamTexture(WebCamTexture.devices[0].name, INPUT_SIZE, INPUT_SIZE, 30);
        camTex.Play();

        // Model
        var model = ModelLoader.Load(modelAsset);
        worker = WorkerFactory.CreateWorker(WorkerFactory.Type.Compute, model);

        // RenderTextures
        inputRT  = new RenderTexture(INPUT_SIZE, INPUT_SIZE, 0, RenderTextureFormat.ARGB32);
        outputRT = new RenderTexture(INPUT_SIZE, INPUT_SIZE, 0, RenderTextureFormat.R8);

        floorMat.SetTexture("_MaskTex", outputRT);
        floorMat.SetTexture("_FloorTex", floorTexture);

        StartCoroutine(InferenceLoop());
    }

    IEnumerator InferenceLoop()
    {
        WaitForEndOfFrame wait = new WaitForEndOfFrame();
        while (true)
        {
            yield return wait;
            if (!camTex.didUpdateThisFrame) continue;

            // 1. Copy webcam → 520×520 GPU RT
            Graphics.Blit(camTex, inputRT);

            // 2. Tensor từ RT
            Tensor input = new Tensor(inputRT, channels: 3);
            worker.Execute(input);
            Tensor output = worker.PeekOutput();   // 1×21×520×520

            // 3. Barracuda có thể dùng SoftmaxLayer nếu export có; ở đây ta để CPU
            output.ToRenderTexture(outputRT, 0, 0);  // copy raw logits
            // (shader sẽ chọn class 12)

            // 4. Shader params
            Matrix4x4 vp = GL.GetGPUProjectionMatrix(cam.projectionMatrix, false) * cam.worldToCameraMatrix;
            floorMat.SetMatrix("_InverseVP", vp.inverse);
            floorMat.SetFloat("_FloorY", floorY);

            input.Dispose();
            output.Dispose();
        }
    }

    void OnRenderImage(RenderTexture src, RenderTexture dst)
    {
        Graphics.Blit(src, dst, floorMat);
    }

    void OnDestroy()
    {
        worker?.Dispose();
        camTex?.Stop();
        inputRT?.Release();
        outputRT?.Release();
    }
}