Shader "Hidden/FloorOverlay"
{
    Properties {
        _MaskTex ("Mask Texture", 2D) = "white" {}
        _FloorTex ("Floor Texture", 2D) = "white" {}
    }
    SubShader {
        Tags { "RenderType"="Opaque" }
        Pass {
            ZWrite Off Cull Off ZTest Always
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            sampler2D _MainTex;   // Unity passes ARCameraBackground
            sampler2D _MaskTex;   // mask AI
            sampler2D _FloorTex;  // texture sàn

            struct v2f { float4 pos:SV_POSITION; float2 uv: TEXCOORD0; };
            v2f vert(appdata_img v) {
                v2f o; o.pos = UnityObjectToClipPos(v.vertex); o.uv = v.texcoord; return o;
            }
            fixed4 frag(v2f i):SV_Target {
                fixed4 cam = tex2D(_MainTex, i.uv);
                fixed4 floor = tex2D(_FloorTex, i.uv);
                float m = tex2D(_MaskTex, i.uv).r;
                return lerp(cam, floor, m);
            }
            ENDHLSL
        }
    }
}