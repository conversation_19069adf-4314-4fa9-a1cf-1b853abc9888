Shader "Custom/FloorFromSegment520"
{
    Properties
    {
        _FloorTex   ("Floor Texture", 2D) = "white" {}
        _MaskTex    ("Segmentation Mask", 2D) = "black" {}
        _FloorY     ("Floor Y (world)", Float) = 0
        _Threshold  ("Y Tolerance", Float) = 0.05
        _Tiling     ("Tiling", Float) = 5
    }

    SubShader
    {
        Tags { "Queue" = "Transparent" "RenderType" = "Transparent" }
        Blend SrcAlpha OneMinusSrcAlpha
        ZWrite Off

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            struct v2f
            {
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
            };

            sampler2D _FloorTex, _MaskTex, _CameraDepthTexture;
            float _FloorY, _Threshold, _Tiling;
            float4x4 _InverseVP;

            v2f vert (float4 vertex : POSITION)
            {
                v2f o;
                o.pos = UnityObjectToClipPos(vertex);
                o.uv = vertex.xy * 0.5 + 0.5;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                // class index (red channel)
                float classId = tex2D(_MaskTex, i.uv).r * 255;
                bool isFloor  = (abs(classId - 12.0) < 0.5);

                // Reconstruct worldPos
                float depth = SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, i.uv);
                float4 clip = float4(i.uv * 2 - 1, depth, 1);
                float4 world = mul(_InverseVP, clip);
                world /= world.w;

                if (isFloor && abs(world.y - _FloorY) < _Threshold)
                {
                    float2 uv = world.xz * _Tiling;
                    return tex2D(_FloorTex, uv);
                }
                discard;
                return 0;
            }
            ENDCG
        }
    }
}